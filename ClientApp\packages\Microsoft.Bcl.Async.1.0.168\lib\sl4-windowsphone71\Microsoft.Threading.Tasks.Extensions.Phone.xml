<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Threading.Tasks.Extensions.Phone</name>
    </assembly>
    <members>
        <member name="T:AsyncPlatformExtensions">
            <summary>
                Provides asynchronous wrappers for .NET Framework operations.
            </summary>
            <summary>
                Provides asynchronous wrappers for .NET Framework operations.
            </summary>
        </member>
        <member name="M:AsyncPlatformExtensions.DownloadStringTaskAsync(System.Net.WebClient,System.String)">
            <summary>Downloads the resource with the specified URI as a string, asynchronously.</summary>
            <param name="webClient">The WebClient.</param>
            <param name="address">The URI from which to download data.</param>
            <returns>A Task that contains the downloaded string.</returns>
        </member>
        <member name="M:AsyncPlatformExtensions.DownloadStringTaskAsync(System.Net.WebClient,System.Uri)">
            <summary>Downloads the resource with the specified URI as a string, asynchronously.</summary>
            <param name="webClient">The WebClient.</param>
            <param name="address">The URI from which to download data.</param>
            <returns>A Task that contains the downloaded string.</returns>
        </member>
        <member name="M:AsyncPlatformExtensions.OpenReadTaskAsync(System.Net.WebClient,System.String)">
            <summary>Opens a readable stream for the data downloaded from a resource, asynchronously.</summary>
            <param name="webClient">The WebClient.</param>
            <param name="address">The URI for which the stream should be opened.</param>
            <returns>A Task that contains the opened stream.</returns>
        </member>
        <member name="M:AsyncPlatformExtensions.OpenReadTaskAsync(System.Net.WebClient,System.Uri)">
            <summary>Opens a readable stream for the data downloaded from a resource, asynchronously.</summary>
            <param name="webClient">The WebClient.</param>
            <param name="address">The URI for which the stream should be opened.</param>
            <returns>A Task that contains the opened stream.</returns>
        </member>
        <member name="M:AsyncPlatformExtensions.OpenWriteTaskAsync(System.Net.WebClient,System.String)">
            <summary>Opens a writeable stream for uploading data to a resource, asynchronously.</summary>
            <param name="webClient">The WebClient.</param>
            <param name="address">The URI for which the stream should be opened.</param>
            <returns>A Task that contains the opened stream.</returns>
        </member>
        <member name="M:AsyncPlatformExtensions.OpenWriteTaskAsync(System.Net.WebClient,System.Uri)">
            <summary>Opens a writeable stream for uploading data to a resource, asynchronously.</summary>
            <param name="webClient">The WebClient.</param>
            <param name="address">The URI for which the stream should be opened.</param>
            <returns>A Task that contains the opened stream.</returns>
        </member>
        <member name="M:AsyncPlatformExtensions.OpenWriteTaskAsync(System.Net.WebClient,System.String,System.String)">
            <summary>Opens a writeable stream for uploading data to a resource, asynchronously.</summary>
            <param name="webClient">The WebClient.</param>
            <param name="address">The URI for which the stream should be opened.</param>
            <param name="method">The HTTP method that should be used to open the stream.</param>
            <returns>A Task that contains the opened stream.</returns>
        </member>
        <member name="M:AsyncPlatformExtensions.OpenWriteTaskAsync(System.Net.WebClient,System.Uri,System.String)">
            <summary>Opens a writeable stream for uploading data to a resource, asynchronously.</summary>
            <param name="webClient">The WebClient.</param>
            <param name="address">The URI for which the stream should be opened.</param>
            <param name="method">The HTTP method that should be used to open the stream.</param>
            <returns>A Task that contains the opened stream.</returns>
        </member>
        <member name="M:AsyncPlatformExtensions.UploadStringTaskAsync(System.Net.WebClient,System.String,System.String)">
            <summary>Uploads data in a string to the specified resource, asynchronously.</summary>
            <param name="webClient">The WebClient.</param>
            <param name="address">The URI to which the data should be uploaded.</param>
            <param name="data">The data to upload.</param>
            <returns>A Task containing the data in the response from the upload.</returns>
        </member>
        <member name="M:AsyncPlatformExtensions.UploadStringTaskAsync(System.Net.WebClient,System.Uri,System.String)">
            <summary>Uploads data in a string to the specified resource, asynchronously.</summary>
            <param name="webClient">The WebClient.</param>
            <param name="address">The URI to which the data should be uploaded.</param>
            <param name="data">The data to upload.</param>
            <returns>A Task containing the data in the response from the upload.</returns>
        </member>
        <member name="M:AsyncPlatformExtensions.UploadStringTaskAsync(System.Net.WebClient,System.String,System.String,System.String)">
            <summary>Uploads data in a string to the specified resource, asynchronously.</summary>
            <param name="webClient">The WebClient.</param>
            <param name="address">The URI to which the data should be uploaded.</param>
            <param name="method">The HTTP method that should be used to upload the data.</param>
            <param name="data">The data to upload.</param>
            <returns>A Task containing the data in the response from the upload.</returns>
        </member>
        <member name="M:AsyncPlatformExtensions.UploadStringTaskAsync(System.Net.WebClient,System.Uri,System.String,System.String)">
            <summary>Uploads data in a string to the specified resource, asynchronously.</summary>
            <param name="webClient">The WebClient.</param>
            <param name="address">The URI to which the data should be uploaded.</param>
            <param name="method">The HTTP method that should be used to upload the data.</param>
            <param name="data">The data to upload.</param>
            <returns>A Task containing the data in the response from the upload.</returns>
        </member>
        <member name="M:AsyncPlatformExtensions.GetUri(System.Net.WebClient,System.String)">
            <summary>Converts a path to a Uri using the WebClient's logic.</summary>
            <remarks>Based on WebClient's private GetUri method.</remarks>
        </member>
        <member name="M:AsyncPlatformExtensions.GetUri(System.Net.WebClient,System.Uri)">
            <summary>Converts a path to a Uri using the WebClient's logic.</summary>
            <remarks>Based on WebClient's private GetUri method.</remarks>
        </member>
        <member name="M:AsyncPlatformExtensions.InvokeAsync(System.Windows.Threading.Dispatcher,System.Action)">
            <summary>Asynchronously invokes an Action on the Dispatcher.</summary>
            <param name="dispatcher">The Dispatcher.</param>
            <param name="action">The action to invoke.</param>
            <returns>A Task that represents the execution of the action.</returns>
        </member>
        <member name="M:AsyncPlatformExtensions.InvokeAsync``1(System.Windows.Threading.Dispatcher,System.Func{``0})">
            <summary>Asynchronously invokes an Action on the Dispatcher.</summary>
            <param name="dispatcher">The Dispatcher.</param>
            <param name="function">The function to invoke.</param>
            <returns>A Task that represents the execution of the function.</returns>
        </member>
        <member name="T:VoidTaskResult">
            <summary>Used with Task(of void)</summary>
        </member>
        <member name="M:System.Threading.Tasks.TaskServices.FromCancellation(System.Threading.CancellationToken)">
            <summary>Returns a canceled task.</summary>
            <param name="cancellationToken">The cancellation token.</param>
            <returns>The canceled task.</returns>
        </member>
        <member name="M:System.Threading.Tasks.TaskServices.FromCancellation``1(System.Threading.CancellationToken)">
            <summary>Returns a canceled task.</summary>
            <typeparam name="TResult">Specifies the type of the result.</typeparam>
            <param name="cancellationToken">The cancellation token.</param>
            <returns>The canceled task.</returns>
        </member>
        <member name="M:System.Threading.Tasks.TaskServices.HandleEapCompletion``1(System.Threading.Tasks.TaskCompletionSource{``0},System.Boolean,System.ComponentModel.AsyncCompletedEventArgs,System.Func{``0},System.Action)">
            <summary>
            Completes the Task if the user state matches the TaskCompletionSource.
            </summary>
            <typeparam name="T">Specifies the type of data returned by the Task.</typeparam>
            <param name="tcs">The TaskCompletionSource.</param>
            <param name="e">The completion event arguments.</param>
            <param name="requireMatch">Whether we require the tcs to match the e.UserState.</param>
            <param name="getResult">A function that gets the result with which to complete the task.</param>
            <param name="unregisterHandler">An action used to unregister work when the operaiton completes.</param>
        </member>
    </members>
</doc>
