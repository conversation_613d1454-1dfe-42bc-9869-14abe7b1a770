﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>DevExpress.Drawing.v24.2</name>
  </assembly>
  <members>
    <member name="N:DevExpress.Drawing">
      <summary>
        <para>Contains cross-platform counterparts of the System.Drawing classes not supported in non-Windows environments.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Drawing.DrawingEngine">
      <summary>
        <para>Lists values that indicate the drawing engine.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DrawingEngine.Default">
      <summary>
        <para>Default drawing engine.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DrawingEngine.GdiPlus">
      <summary>
        <para>GDI+ drawing engine.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DrawingEngine.Skia">
      <summary>
        <para>Skia drawing engine.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Drawing.DXAdjustableArrowCap">
      <summary>
        <para>An adjustable arrow-shaped line cap.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.DXAdjustableArrowCap.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Drawing.DXAdjustableArrowCap"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.DXAdjustableArrowCap.#ctor(System.Single,System.Single,System.Single,System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Drawing.DXAdjustableArrowCap"/> class with specified settings.</para>
      </summary>
      <param name="width">The width of the arrow.</param>
      <param name="height">The height of the arrow.</param>
      <param name="widthScale">The amount by which to scale the line cap with respect to the width of the DXPen object.</param>
      <param name="filled">true to fill the arrow cap; otherwise, false.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXAdjustableArrowCap.Equals(System.Object)">
      <summary>
        <para>Determines whether the specified object is equal to the current DXAdjustableArrowCap instance.</para>
      </summary>
      <param name="obj">The object to compare.</param>
      <returns>true if the obj parameter is DXFont and has the same property values as the current DXAdjustableArrowCap instance; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.Drawing.DXAdjustableArrowCap.Filled">
      <summary>
        <para>Gets or sets whether the arrow cap is filled.</para>
      </summary>
      <value>true if the arrow cap is filled; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.Drawing.DXAdjustableArrowCap.GetHashCode">
      <summary>
        <para>Gets the hash code for the DXAdjustableArrowCap object.</para>
      </summary>
      <returns>The hash code.</returns>
    </member>
    <member name="P:DevExpress.Drawing.DXAdjustableArrowCap.Height">
      <summary>
        <para>Gets or sets the height of the arrow cap.</para>
      </summary>
      <value>The cap height.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXAdjustableArrowCap.MiddleInset">
      <summary>
        <para>Gets or sets the number of units between the outline of the arrow cap and the fill.</para>
      </summary>
      <value>The number of units between the outline of the arrow cap and the fill of the arrow cap.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXAdjustableArrowCap.Width">
      <summary>
        <para>Gets or sets the width of the arrow cap.</para>
      </summary>
      <value>The arrow cap width.</value>
    </member>
    <member name="T:DevExpress.Drawing.DXBitmap">
      <summary>
        <para>Encapsulates a bitmap which consists of the pixel data for a graphics image and its attributes.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.DXBitmap.#ctor(System.Int32,System.Int32)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Drawing.DXBitmap"/> class with specified settings.</para>
      </summary>
      <param name="width">The bitmap width, in pixels.</param>
      <param name="height">The bitmap height, in pixels.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXBitmap.Clone">
      <summary>
        <para>Clones the current DXBitmap object.</para>
      </summary>
      <returns>A copy of the DXBitmap object.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXBitmap.Crop(System.Drawing.Rectangle)">
      <summary>
        <para>Crops the bitmap to the specified rectangle.</para>
      </summary>
      <param name="rectangle">An area to which the bitmap should be cropped.</param>
      <returns>The resulting bitmap.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXBitmap.Crop(System.Drawing.RectangleF)">
      <summary>
        <para>Crops the bitmap to the specified rectangle.</para>
      </summary>
      <param name="rectangle">An area to which the bitmap should be cropped.</param>
      <returns>The resulting bitmap.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXBitmap.Equals(DevExpress.Drawing.DXBitmap)">
      <summary>
        <para>Determines whether the specified object is equal to the current DXBitmap instance.</para>
      </summary>
      <param name="other">An object to compare.</param>
      <returns>true if the object parameter has the same property values as the current DXBitmap instance; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXBitmap.Equals(System.Object)">
      <summary>
        <para>Determines whether the specified object is equal to the current DXBitmap instance.</para>
      </summary>
      <param name="obj">An object to compare.</param>
      <returns>true if the object parameter has the same property values as the current DXBitmap instance; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXBitmap.GetHashCode">
      <summary>
        <para>Gets a hash code for the DXBitmap object.</para>
      </summary>
      <returns>The hash code.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXBitmap.GetPixel(System.Int32,System.Int32)">
      <summary>
        <para>Gets the color of the specified pixel.</para>
      </summary>
      <param name="x">The x-coordinate of the pixel to retrieve.</param>
      <param name="y">The y-coordinate of the pixel to retrieve.</param>
      <returns>The color of the specified pixel.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXBitmap.RotateFlip(DevExpress.Drawing.DXRotateFlipType)">
      <summary>
        <para>Rotates and flips the image.</para>
      </summary>
      <param name="rotateFlipType">An enumeration value that indicates the rotation and flip type.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXBitmap.SetResolution(System.Single,System.Single)">
      <summary>
        <para>Sets the resolution of this DXBitmap object.</para>
      </summary>
      <param name="horizontalResolution">The horizontal resolution in dots per inch.</param>
      <param name="verticalResolution">The vertical resolution in dots per inch</param>
    </member>
    <member name="T:DevExpress.Drawing.DXBlend">
      <summary>
        <para>Defines blending patterns.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.DXBlend.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Drawing.DXBlend"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.DXBlend.#ctor(System.Int32)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Drawing.DXBlend"/> class with specified settings.</para>
      </summary>
      <param name="count">The number of elements in Factors and Positions arrays.</param>
    </member>
    <member name="P:DevExpress.Drawing.DXBlend.Factors">
      <summary>
        <para>Gets or sets an array of blend factors for the gradient.</para>
      </summary>
      <value>An array of blend factors that specify the percentages of the starting color and the ending color to be used at the corresponding position.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXBlend.Positions">
      <summary>
        <para>Gets or sets an array of blend positions for the gradient.</para>
      </summary>
      <value>An array of blend positions that specify a distance along the gradient line as a percentage.</value>
    </member>
    <member name="T:DevExpress.Drawing.DXBrush">
      <summary>
        <para>Base class for brushes.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.DXBrush.Clone">
      <summary>
        <para>Clones the current DXBrush object.</para>
      </summary>
      <returns>A copy of the current DXBrush object.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXBrush.Dispose">
      <summary>
        <para>Disposes of the DXBrush object.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Drawing.DXClipCombineMode">
      <summary>
        <para>Lists values that indicate how clipping regions can be combined.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXClipCombineMode.Intersect">
      <summary>
        <para>Two clipping regions are combined by taking their intersection.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXClipCombineMode.Replace">
      <summary>
        <para>One clipping region replaces by another.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Drawing.DXColorBlend">
      <summary>
        <para>Defines arrays of colors and positions used to interpolate color blending in a multicolor gradient.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.DXColorBlend.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Drawing.DXColorBlend"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.DXColorBlend.#ctor(System.Int32)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Drawing.DXColorBlend"/> class with specified settings.</para>
      </summary>
      <param name="count">The numbers of colors and positions in the blend.</param>
    </member>
    <member name="P:DevExpress.Drawing.DXColorBlend.Colors">
      <summary>
        <para>Gets or sets the colors used at specified positions along a gradient.</para>
      </summary>
      <value>An array of colors used at specified positions along a gradient.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXColorBlend.Positions">
      <summary>
        <para>Gets or sets the positions along a gradient line.</para>
      </summary>
      <value>An array of positions along a gradient line.</value>
    </member>
    <member name="T:DevExpress.Drawing.DXCompositingMode">
      <summary>
        <para>Lists values that indicate how to combine source colors with background colors.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXCompositingMode.SourceCopy">
      <summary>
        <para>When a source color is rendered, it overwrites the background color.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXCompositingMode.SourceOver">
      <summary>
        <para>When a source color is rendered, it blends with the background color. The blend is determined by the alpha component of the color being rendered.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Drawing.DXCustomLineCap">
      <summary>
        <para>A custom user-defined line cap.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.DXCustomLineCap.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Drawing.DXCustomLineCap"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Drawing.DXCustomLineCap.BaseCap">
      <summary>
        <para>Gets or sets the base cap for a custom cap.</para>
      </summary>
      <value>An enumeration value that indicates a base cap type.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXCustomLineCap.BaseInset">
      <summary>
        <para>Gets or sets the distance between the cap and the line.</para>
      </summary>
      <value>The distance between the start of the cap and the end of the line.</value>
    </member>
    <member name="M:DevExpress.Drawing.DXCustomLineCap.Equals(System.Object)">
      <summary>
        <para>Determines whether the specified object is equal to the current DXCustomLineCap instance.</para>
      </summary>
      <param name="obj">An object to compare.</param>
      <returns>true if the obj parameter has the same property values as the current DXCustomLineCap instance; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.Drawing.DXCustomLineCap.FillPath">
      <summary>
        <para>Gets or sets the fill for the custom cap.</para>
      </summary>
      <value>The custom cap fill.</value>
    </member>
    <member name="M:DevExpress.Drawing.DXCustomLineCap.GetHashCode">
      <summary>
        <para>Gets the hash code for the object.</para>
      </summary>
      <returns>The hash code.</returns>
    </member>
    <member name="P:DevExpress.Drawing.DXCustomLineCap.StrokePath">
      <summary>
        <para>Gets or sets the outline of the custom cap.</para>
      </summary>
      <value>The cap outline.</value>
    </member>
    <member name="T:DevExpress.Drawing.DXCustomLineCapBase">
      <summary>
        <para>A base class for custom user-defined line caps.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.DXCustomLineCapBase.Equals(System.Object)">
      <summary>
        <para>Determines whether the specified object is equal to the current DXCustomLineCapBase instance.</para>
      </summary>
      <param name="obj">An object to compare.</param>
      <returns>true if the obj parameter has the same property values as the current DXCustomLineCapBase instance; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXCustomLineCapBase.GetHashCode">
      <summary>
        <para>Gets the hash code for the DXCustomLineCapBase instance.</para>
      </summary>
      <returns>The hash code.</returns>
    </member>
    <member name="P:DevExpress.Drawing.DXCustomLineCapBase.StrokeEndCap">
      <summary>
        <para>Gets or sets the caps used to end lines that make up this custom cap.</para>
      </summary>
      <value>An enumeration value that indicates a cap used at the end of a line.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXCustomLineCapBase.StrokeJoin">
      <summary>
        <para>Gets or sets how lines the compose the custom line cap are joined.</para>
      </summary>
      <value>An enumeration value that indicates the line join mode.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXCustomLineCapBase.StrokeMiterLimit">
      <summary>
        <para>Gets or sets the limit of the thickness of the join on a mitered corner.</para>
      </summary>
      <value>The limit of the thickness of the join on a mitered corner.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXCustomLineCapBase.StrokeStartCap">
      <summary>
        <para>Gets or sets the caps used to start lines that make up this custom cap.</para>
      </summary>
      <value>The enumeration value that indicates a cap used at the start of a line.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXCustomLineCapBase.WidthScale">
      <summary>
        <para>Gets or sets the amount by which to scale the line cap with respect to the width of the DXPen object.</para>
      </summary>
      <value>The scaling amount.</value>
    </member>
    <member name="T:DevExpress.Drawing.DXDashCap">
      <summary>
        <para>Lists values that indicate the type of graphic shape to use on both ends of each dash in a dashed line.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXDashCap.Flat">
      <summary>
        <para>A square cap that squares off both ends of each dash.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXDashCap.Round">
      <summary>
        <para>A circular cap that rounds off both ends of each dash.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXDashCap.Square">
      <summary>
        <para>A square cap that rounds off both ends of each dash.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXDashCap.Triangle">
      <summary>
        <para>A triangular cap that points both ends of each dash.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Drawing.DXDashStyle">
      <summary>
        <para>Lists values that indicate the dashed line style.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXDashStyle.Custom">
      <summary>
        <para>A user-defined dash style.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXDashStyle.Dash">
      <summary>
        <para>A line that consists of short dashes.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXDashStyle.DashDot">
      <summary>
        <para>A line that consists of a repeating short dash-dot pattern.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXDashStyle.DashDotDot">
      <summary>
        <para>A line that consists of a repeating short dash-dot-dot pattern.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXDashStyle.Dot">
      <summary>
        <para>A line that consists of dots.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXDashStyle.Solid">
      <summary>
        <para>A solid line.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Drawing.DXFillMode">
      <summary>
        <para>Lists values that indicate fill modes of a closed path interior.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXFillMode.Alternate">
      <summary>
        <para>The alternate fill mode.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXFillMode.Winding">
      <summary>
        <para>The winding fill mode.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Drawing.DXFont">
      <summary>
        <para>Contains the text format settings such as font size and style.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.DXFont.#ctor(DevExpress.Drawing.DXFont,DevExpress.Drawing.DXFontStyle)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Drawing.DXFont"/> class with specified settings.</para>
      </summary>
      <param name="prototype">A <see cref="T:DevExpress.Drawing.DXFont"/> object that specifies the font.</param>
      <param name="newStyle">A DXFontStyle object that specifies the font style.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXFont.#ctor(System.String,System.Single,DevExpress.Drawing.DXFontStyle,DevExpress.Drawing.DXGraphicsUnit,DevExpress.Drawing.DXFontAdditionalProperty[])">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Drawing.DXFont"/> class with specified settings.</para>
      </summary>
      <param name="name">A string that specifies the font name.</param>
      <param name="size">The size of the font.</param>
      <param name="style">A DXFontStyle object that specifies the font style.</param>
      <param name="unit">A DXGraphicsUnit object that specifies the font’s unit of measure.</param>
      <param name="additionalProperties">An array of DXFontAdditionalProperty objects that are additional font properties.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXFont.#ctor(System.String,System.Single,DevExpress.Drawing.DXFontStyle,DevExpress.Drawing.DXGraphicsUnit)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Drawing.DXFont"/> class with specified settings.</para>
      </summary>
      <param name="name">A string that specifies the font name.</param>
      <param name="size">The size of the font.</param>
      <param name="style">A DXFontStyle object that specifies the font style.</param>
      <param name="unit">A DXGraphicsUnit object that specifies the font’s unit of measure.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXFont.#ctor(System.String,System.Single,DevExpress.Drawing.DXFontStyle)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Drawing.DXFont"/> class with specified settings.</para>
      </summary>
      <param name="name">A string that specifies the font name.</param>
      <param name="size">The size of the font in points.</param>
      <param name="style">A DXFontStyle object that specifies the font style.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXFont.#ctor(System.String,System.Single)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Drawing.DXFont"/> class with specified settings.</para>
      </summary>
      <param name="name">A string that specifies the font name.</param>
      <param name="size">The size of the font in points.</param>
    </member>
    <member name="P:DevExpress.Drawing.DXFont.Bold">
      <summary>
        <para>Gets a value that indicates whether the <see cref="T:DevExpress.Drawing.DXFont">font</see> is bold.</para>
      </summary>
      <value>true if the font is bold; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.Drawing.DXFont.Clone">
      <summary>
        <para>Creates a copy of the <see cref="T:DevExpress.Drawing.DXFont"/> instance.</para>
      </summary>
      <returns>The copy of the <see cref="T:DevExpress.Drawing.DXFont"/> instance.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXFont.Equals(DevExpress.Drawing.DXFont)">
      <summary>
        <para>Determines whether the specified <see cref="T:DevExpress.Drawing.DXFont"/> object is equal to the current DXFont instance.</para>
      </summary>
      <param name="other">A DXFont object to test.</param>
      <returns>true if the DXFont parameter has the same property values as the current DXFont instance; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXFont.Equals(System.Object)">
      <summary>
        <para>Determines whether the specified object is equal to the current DXFont instance.</para>
      </summary>
      <param name="obj">The object to test.</param>
      <returns>true if the obj parameter is DXFont and has the same property values as the current DXFont instance; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXFont.GetHashCode">
      <summary>
        <para>Gets the hash code for DXFont.</para>
      </summary>
      <returns>The hash code.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXFont.GetHeight(DevExpress.Drawing.DXGraphicsUnit)">
      <summary>
        <para>Gets the font’s line spacing in the specified graphics units.</para>
      </summary>
      <param name="graphicsUnit">An enumeration value that indicates the measurement unit for the line spacing.</param>
      <returns>The line spacing of the font.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXFont.GetHeight(System.Single)">
      <summary>
        <para>Gets the font’s line spacing in pixels when drawn on a device with the specified DPI.</para>
      </summary>
      <param name="dpi">The vertical resolution used to calculate the font height in dots per inch.</param>
      <returns>The font height in pixels.</returns>
    </member>
    <member name="P:DevExpress.Drawing.DXFont.Height">
      <summary>
        <para>Obtains the font height.</para>
      </summary>
      <value>The font height.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXFont.Italic">
      <summary>
        <para>Gets a value that indicates whether the italic style is applied to the font.</para>
      </summary>
      <value>true to indicate the font has the italic style applied; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXFont.Name">
      <summary>
        <para>Gets the <see cref="T:DevExpress.Drawing.DXFont">font</see> name.</para>
      </summary>
      <value>The name of the font.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXFont.Size">
      <summary>
        <para>Gets the size of the font in units defined by the <see cref="P:DevExpress.Drawing.DXFont.Unit">DXFont.Unit</see> property.</para>
      </summary>
      <value>The size of the font.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXFont.SizeInPoints">
      <summary>
        <para>Gets the size of the font in points.</para>
      </summary>
      <value>The size of the font in points.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXFont.Strikeout">
      <summary>
        <para>Gets a value that indicates whether the font has a horizontal line through it.</para>
      </summary>
      <value>true if the font has a horizontal strikeout style; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXFont.Style">
      <summary>
        <para>Returns style information applied to the font.</para>
      </summary>
      <value>A DXFontStyle object that specifies the font style.</value>
    </member>
    <member name="M:DevExpress.Drawing.DXFont.ToString">
      <summary>
        <para>Returns a string that specifies `DXFont’.</para>
      </summary>
      <returns>A string that specifies DXFont.</returns>
    </member>
    <member name="P:DevExpress.Drawing.DXFont.Underline">
      <summary>
        <para>Gets a value that indicates whether the font is underlined.</para>
      </summary>
      <value>true if the font is underlined; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXFont.Unit">
      <summary>
        <para>Obtains the font measurement unit.</para>
      </summary>
      <value>The measurement unit of the font.</value>
    </member>
    <member name="T:DevExpress.Drawing.DXFontAdditionalProperty">
      <summary>
        <para>Contains additional font properties.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.DXFontAdditionalProperty.#ctor(System.String,System.Object)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Drawing.DXFontAdditionalProperty"/> class with specified settings.</para>
      </summary>
      <param name="name">The name of the property.</param>
      <param name="value">The property value.</param>
    </member>
    <member name="F:DevExpress.Drawing.DXFontAdditionalProperty.GdiCharSet">
      <summary>
        <para>The GdGdiCharSet property name.</para>
      </summary>
      <value>The property name.</value>
    </member>
    <member name="F:DevExpress.Drawing.DXFontAdditionalProperty.GdiVerticalFont">
      <summary>
        <para>The GdiVerticalFont property name.</para>
      </summary>
      <value>The property name.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXFontAdditionalProperty.Name">
      <summary>
        <para>Obtains the property name.</para>
      </summary>
      <value>The property name.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXFontAdditionalProperty.Value">
      <summary>
        <para>Obtains the property value.</para>
      </summary>
      <value>The property value.</value>
    </member>
    <member name="T:DevExpress.Drawing.DXFontData">
      <summary>
        <para>Contains information about a font in <see cref="T:DevExpress.Drawing.DXFontRepository"/>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.DXFontData.#ctor(System.String,System.String,System.Int32,System.Boolean,System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Drawing.DXFontData"/> class with specified settings.</para>
      </summary>
      <param name="filePath">The path to the font file.</param>
      <param name="name">The font name.</param>
      <param name="ttcIndex">A zero-based index of the font within the TTC file.</param>
      <param name="bold">true if the font is bold; otherwise, false.</param>
      <param name="italic">true if the font style is italic; otherwise, false.</param>
    </member>
    <member name="P:DevExpress.Drawing.DXFontData.Bold">
      <summary>
        <para>Indicates whether the font is bold.</para>
      </summary>
      <value>true if the font is bold; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXFontData.FilePath">
      <summary>
        <para>Returns the path to the font file (if the font is loaded from a file).</para>
      </summary>
      <value>The path to the font file. Null (Nothing in Visual Basic) if the font is loaded from a stream or byte array.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXFontData.Italic">
      <summary>
        <para>Indicates whether the font style is italic.</para>
      </summary>
      <value>true if the font style is italic; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXFontData.Name">
      <summary>
        <para>Returns the font name (including the Font Family and Font Subfamily names).</para>
      </summary>
      <value>The font name.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXFontData.TtcIndex">
      <summary>
        <para>Returns the font index in the TrueType Collection (TTC) file.</para>
      </summary>
      <value>A zero-based index of the font within the collection.</value>
    </member>
    <member name="T:DevExpress.Drawing.DXFontRepository">
      <summary>
        <para>A repository of user-defined fonts.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.DXFontRepository.AddFont(System.Byte[])">
      <summary>
        <para>Loads a font from a byte array.</para>
      </summary>
      <param name="fontData">A byte array that contains font data.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXFontRepository.AddFont(System.IO.Stream)">
      <summary>
        <para>Loads a font from a stream.</para>
      </summary>
      <param name="fontStream">A stream from which to load the font.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXFontRepository.AddFont(System.String)">
      <summary>
        <para>Loads a font from a file.</para>
      </summary>
      <param name="fontFileName">The full path to the font file.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXFontRepository.Clear">
      <summary>
        <para>Removes all fonts from the font repository.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.DXFontRepository.Dispose">
      <summary>
        <para>Releases resources associated with the <see cref="T:DevExpress.Drawing.DXFontRepository"/> instance.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.DXFontRepository.GetFonts">
      <summary>
        <para>Returns a list of all fonts in <see cref="T:DevExpress.Drawing.DXFontRepository"/>.</para>
      </summary>
      <returns>A list of <see cref="T:DevExpress.Drawing.DXFontData"/> objects that contain information about fonts in the repository.</returns>
    </member>
    <member name="P:DevExpress.Drawing.DXFontRepository.Instance">
      <summary>
        <para>Returns an instance of the <see cref="T:DevExpress.Drawing.DXFontRepository"/> class.</para>
      </summary>
      <value>The DXFontRepository class instance.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXFontRepository.IsEmpty">
      <summary>
        <para>Indicates whether the font repository is empty.</para>
      </summary>
      <value>true if the font repository contains no fonts; otherwise, false.</value>
    </member>
    <member name="E:DevExpress.Drawing.DXFontRepository.QueryNotFoundFont">
      <summary>
        <para>Occurs if a document requires a font that is not available in the hosting environment.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Drawing.DXFontStyle">
      <summary>
        <para>Lists values that indicate style information applied to text.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXFontStyle.Bold">
      <summary>
        <para>Bold text.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXFontStyle.Italic">
      <summary>
        <para>Italic text.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXFontStyle.Regular">
      <summary>
        <para>Normal text.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXFontStyle.Strikeout">
      <summary>
        <para>Strike-through text.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXFontStyle.Underline">
      <summary>
        <para>Underlined text.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Drawing.DXGraphics">
      <summary>
        <para>Allows you to draw graphics content.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphics.Clear(System.Drawing.Color)">
      <summary>
        <para>Clears the entire drawing surface and fills it with the specified background color.</para>
      </summary>
      <param name="color">The background color of the drawing surface.</param>
    </member>
    <member name="P:DevExpress.Drawing.DXGraphics.ClipBounds">
      <summary>
        <para>Obtains the clipping region of the graphics layout.</para>
      </summary>
      <value>The clipping region.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXGraphics.CompositingMode">
      <summary>
        <para>Gets or sets how to draw composited images</para>
      </summary>
      <value>An enumeration value that determines the compositing mode.</value>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphics.Dispose">
      <summary>
        <para>Disposes of the DXGraphics object.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Drawing.DXGraphics.DpiX">
      <summary>
        <para>Gets the horizontal resolution of the DXGraphics object.</para>
      </summary>
      <value>The horizontal resolution value.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXGraphics.DpiY">
      <summary>
        <para>Gets the vertical resolution of the DXGraphics object.</para>
      </summary>
      <value>The vertical resolution value.</value>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphics.DrawArc(DevExpress.Drawing.DXPen,System.Drawing.RectangleF,System.Single,System.Single)">
      <summary>
        <para>Draws an arc specified by a rectangle.</para>
      </summary>
      <param name="pen">An object that determines the color, width, and style of the arc.</param>
      <param name="bounds">A rectangle that defines the ellipsis bounds.</param>
      <param name="startAngle">Angle in degrees measured clockwise from the x-axis to the starting point of the arc.</param>
      <param name="sweepAngle">Angle in degrees measured clockwise from the startAngle parameter to ending point of the arc.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphics.DrawBezier(DevExpress.Drawing.DXPen,System.Drawing.PointF,System.Drawing.PointF,System.Drawing.PointF,System.Drawing.PointF)">
      <summary>
        <para>Draws a Bezier spline defined by points.</para>
      </summary>
      <param name="pen">An object that determines the color, width, and style of the curve.</param>
      <param name="pt1">The starting point of the curve.</param>
      <param name="pt2">The first control point of the curve.</param>
      <param name="pt3">The second control point of the curve.</param>
      <param name="pt4">The ending point of the curve.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphics.DrawBeziers(DevExpress.Drawing.DXPen,System.Drawing.PointF[])">
      <summary>
        <para>Draws a series of Bezier splines defined by an array of points.</para>
      </summary>
      <param name="pen">An object that determines the color, width, and style of the curve.</param>
      <param name="points">The points that determine the curve. The number of points in the array should be a multiple of 3 plus 1 (4, 7, 10, and so on).</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphics.DrawEllipse(DevExpress.Drawing.DXPen,System.Drawing.RectangleF)">
      <summary>
        <para>Draws an ellipse in the specified rectangle.</para>
      </summary>
      <param name="pen">An object that determines the color, width, and style of the ellipse.</param>
      <param name="rect">The rectangle that defines the ellipsis bounds.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphics.DrawImage(DevExpress.Drawing.DXImage,System.Drawing.RectangleF,System.Drawing.RectangleF)">
      <summary>
        <para>Draws a portion of the image so that it fits into the specified rectnangle.</para>
      </summary>
      <param name="image">An image to draw.</param>
      <param name="destRect">The image size and location.</param>
      <param name="srcRect">The portion of the image to draw.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphics.DrawImage(DevExpress.Drawing.DXImage,System.Drawing.RectangleF)">
      <summary>
        <para>Draws an image so that it fits into the specified rectnagle.</para>
      </summary>
      <param name="image">An image to draw.</param>
      <param name="rect">Specifies the location and size of the drawn image.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphics.DrawImage(DevExpress.Drawing.DXImage,System.Int32,System.Int32)">
      <summary>
        <para>Draws an image at the specified location.</para>
      </summary>
      <param name="image">An image to draw.</param>
      <param name="x">The x-coordinate of the upper-left corner of the drawn image.</param>
      <param name="y">The y-coordinate of the upper-left corner of the drawn image.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphics.DrawLine(DevExpress.Drawing.DXPen,System.Drawing.PointF,System.Drawing.PointF)">
      <summary>
        <para>Draws a line that connects two points.</para>
      </summary>
      <param name="pen">An object that determines the color, width, and style of the line.</param>
      <param name="pt1">The starting point of the line.</param>
      <param name="pt2">The end point of the line.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphics.DrawLine(DevExpress.Drawing.DXPen,System.Single,System.Single,System.Single,System.Single)">
      <summary>
        <para>Draws a line that connects two points.</para>
      </summary>
      <param name="pen">An object that determines the color, width, and style of the line.</param>
      <param name="x1">The x-coordinate of the first point.</param>
      <param name="y1">The y-coordinate of the first point.</param>
      <param name="x2">The x-coordinate of the second point.</param>
      <param name="y2">The y-coordinate of the second point.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphics.DrawLines(DevExpress.Drawing.DXPen,System.Drawing.Point[])">
      <summary>
        <para>Draws a series of lines that connects an array of points.</para>
      </summary>
      <param name="pen">An object that determines the color, width, and style of the line segments.</param>
      <param name="points">An array of points to connect.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphics.DrawLines(DevExpress.Drawing.DXPen,System.Drawing.PointF[])">
      <summary>
        <para>Draws a series of lines that connects an array of points.</para>
      </summary>
      <param name="pen">An object that determines the color, width, and style of the line segments.</param>
      <param name="points">An array of points to connect.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphics.DrawPath(DevExpress.Drawing.DXPen,DevExpress.Drawing.DXGraphicsPath)">
      <summary>
        <para>Draws a <see cref="T:DevExpress.Drawing.DXGraphicsPath">DXGraphicsPath</see> object.</para>
      </summary>
      <param name="pen">An object that determines the color, width, and style of the path.</param>
      <param name="dxPath">A series of connected lines and curves.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphics.DrawPie(DevExpress.Drawing.DXPen,System.Drawing.Rectangle,System.Single,System.Single)">
      <summary>
        <para>Draws a pie shape.</para>
      </summary>
      <param name="pen">An object that determines the color, width, and style of the pie shape.</param>
      <param name="bounds">The bounding rectangle that defines the pie ellipse.</param>
      <param name="startAngle">Angle measured in degrees clockwise from the x-axis to the first side of the pie shape.</param>
      <param name="sweepAngle">Angle measured in degrees clockwise from the startAngle parameter to the second side of the pie shape.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphics.DrawPolygon(DevExpress.Drawing.DXPen,System.Drawing.PointF[])">
      <summary>
        <para>Draws a polygon defined by an array of points.</para>
      </summary>
      <param name="pen">An object that determines the color, width, and style of the polygon.</param>
      <param name="points">An array of points that are the polygon vertices.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphics.DrawRectangle(DevExpress.Drawing.DXPen,System.Drawing.RectangleF)">
      <summary>
        <para>Draws a rectangle.</para>
      </summary>
      <param name="pen">An object that determines the color, width, and style of the rectangle.</param>
      <param name="bounds">The rectangle to draw.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphics.DrawString(System.String,DevExpress.Drawing.DXFont,DevExpress.Drawing.DXBrush,System.Drawing.PointF,DevExpress.Drawing.DXStringFormat)">
      <summary>
        <para>Draws text in the specified rectangle. Allows you to specify brush, font, and string parameters.</para>
      </summary>
      <param name="text">A text to draw.</param>
      <param name="font">An object that defines text font options.</param>
      <param name="brush">An object that determines the color and texture of the drawn text.</param>
      <param name="point">A point where you can position text.</param>
      <param name="format">An object that specifies text formatting attributes.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphics.DrawString(System.String,DevExpress.Drawing.DXFont,DevExpress.Drawing.DXBrush,System.Drawing.RectangleF,DevExpress.Drawing.DXStringFormat)">
      <summary>
        <para>Draws text in the specified rectangle. Allows you to specify brush, font, and string parameters.</para>
      </summary>
      <param name="text">A text to draw.</param>
      <param name="font">An object that defines text font options.</param>
      <param name="brush">An object that determines the color and texture of the drawn text.</param>
      <param name="layoutRectangle">A location where the text should be drawn.</param>
      <param name="format">An object that specifies text formatting attributes.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphics.FillEllipse(DevExpress.Drawing.DXBrush,System.Drawing.RectangleF)">
      <summary>
        <para>Fills the interior of an ellipse located in the specified rectangle.</para>
      </summary>
      <param name="brush">The brush used to fill the ellipse.</param>
      <param name="rect">An area where you can draw an ellipse.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphics.FillPath(DevExpress.Drawing.DXBrush,DevExpress.Drawing.DXGraphicsPath)">
      <summary>
        <para>Fills the interior of the specified path.</para>
      </summary>
      <param name="brush">The brush used to fill the path.</param>
      <param name="path">A path to fill.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphics.FillPie(DevExpress.Drawing.DXBrush,System.Drawing.Rectangle,System.Single,System.Single)">
      <summary>
        <para>Fills the interior of the pie shape.</para>
      </summary>
      <param name="brush">The brush used to fill the pie shape.</param>
      <param name="bounds">An area where you can draw a pie shape.</param>
      <param name="startAngle">Angle measured in degrees clockwise from the x-axis to the first side of the pie shape.</param>
      <param name="sweepAngle">Angle measured in degrees clockwise from the startAngle parameter to the second side of the pie shape.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphics.FillPolygon(DevExpress.Drawing.DXBrush,System.Drawing.PointF[])">
      <summary>
        <para>Fills the interior of a polygon specified by array points.</para>
      </summary>
      <param name="brush">The brush used to fill the polygon.</param>
      <param name="points">An array of points that specify the polygon vertices.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphics.FillRectangle(DevExpress.Drawing.DXBrush,System.Drawing.RectangleF)">
      <summary>
        <para>Fills the interior of a rectangle located in the specified area.</para>
      </summary>
      <param name="brush">The brush used to fill the rectangle.</param>
      <param name="bounds">An area where you can draw a rectangle.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphics.FillRectangle(DevExpress.Drawing.DXBrush,System.Single,System.Single,System.Single,System.Single)">
      <summary>
        <para>Fills the interior of a rectangle located in the specified area.</para>
      </summary>
      <param name="brush">The brush used to fill the rectangle.</param>
      <param name="x">The x-coordinate of the upper-left corner of the rectangle to fill.</param>
      <param name="y">The y-coordinate of the upper-left corner of the rectangle to fill.</param>
      <param name="width">Width of the rectangle to fill.</param>
      <param name="height">Height of the rectangle to fill.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphics.FillRectangles(DevExpress.Drawing.DXBrush,System.Collections.Generic.IList{System.Drawing.RectangleF})">
      <summary>
        <para>Fills the interior of a rectangle located in the specified area.</para>
      </summary>
      <param name="brush">The brush used to fill the rectangle.</param>
      <param name="rects">A list of rectangles to fill.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphics.FillRegion(DevExpress.Drawing.DXBrush,DevExpress.Drawing.DXRegion)">
      <summary>
        <para>Fills the interior of a region.</para>
      </summary>
      <param name="brush">The brush used to fill the interior.</param>
      <param name="region">The area to fill.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphics.Flush">
      <summary>
        <para>Forces execution of all pending graphics operations and returns immediately without waiting for the operations to finish.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphics.FromImage(DevExpress.Drawing.DXBitmap)">
      <summary>
        <para>Creates a new object from the specified image.</para>
      </summary>
      <param name="bitmap">An image form which to create a new DXGraphics object.</param>
      <returns>The resulting object.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphics.GetClip">
      <summary>
        <para>Gets a region that limits the drawing region of the graphics layout.</para>
      </summary>
      <returns>A region that is currently available for drawing.</returns>
    </member>
    <member name="P:DevExpress.Drawing.DXGraphics.InterpolationMode">
      <summary>
        <para>Gets or sets the interpolation mode associated with the DXGraphics object.</para>
      </summary>
      <value>An enumeration value that indicates how to calculate intermediate values between two endpoints.</value>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphics.MeasureString(System.String,DevExpress.Drawing.DXFont,System.Drawing.SizeF,DevExpress.Drawing.DXStringFormat)">
      <summary>
        <para>Measures the specified string when drawn with the specified font, text layout size, and format.</para>
      </summary>
      <param name="text">A string to measure.</param>
      <param name="font">An object that contains font parameters.</param>
      <param name="layoutArea">Specifies the maximum layout area for the text.</param>
      <param name="format">An object that contains text formatting parameters.</param>
      <returns>The string’s measured size.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphics.MeasureString(System.String,DevExpress.Drawing.DXFont,System.Single,DevExpress.Drawing.DXStringFormat)">
      <summary>
        <para>Measures the specified string when drawn with the specified font, text layout size, and format.</para>
      </summary>
      <param name="text">A string to measure.</param>
      <param name="font">An object that contains font parameters.</param>
      <param name="width">Specifies the maximum width for this text.</param>
      <param name="format">An object that contains text formatting parameters.</param>
      <returns>The string’s measured size.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphics.MultiplyTransform(DevExpress.Drawing.DXMatrix,DevExpress.Drawing.DXMatrixOrder)">
      <summary>
        <para>Multiplies the world transformation by a matrix in the specified order.</para>
      </summary>
      <param name="matrix">A 4x4 matrix that multiplies the world transformation.</param>
      <param name="order">An enumeration member that determines the order of the multiplication.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphics.MultiplyTransform(DevExpress.Drawing.DXMatrix)">
      <summary>
        <para>Multiplies the world transformation by the specified matrix.</para>
      </summary>
      <param name="matrix">A 4x4 matrix that multiplies the world transformation.</param>
    </member>
    <member name="P:DevExpress.Drawing.DXGraphics.PageScale">
      <summary>
        <para>Gets or sets the scaling between world units and page units.</para>
      </summary>
      <value>The scale value.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXGraphics.PageUnit">
      <summary>
        <para>Gets or sets the measure units used for page coordinates.</para>
      </summary>
      <value>An enumeration value that indicates measure units.</value>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphics.ResetTransform">
      <summary>
        <para>Resets the world transformation matrix to the identity matrix.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphics.Restore(System.Object)">
      <summary>
        <para>Restores the state of this DXGraphics to the specified state.</para>
      </summary>
      <param name="state">The state to which to restore the DXGraphics object.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphics.RotateTransform(System.Single,DevExpress.Drawing.DXMatrixOrder)">
      <summary>
        <para>Applies a rotation to the transformation matrix in the specified order.</para>
      </summary>
      <param name="angle">The rotation angle in degrees.</param>
      <param name="order">An enumeration member that specifies whether the rotation is appended or prepended to the matrix transformation.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphics.RotateTransform(System.Single)">
      <summary>
        <para>Applies the specified rotation to the transformation matrix.</para>
      </summary>
      <param name="angle">The rotation angle in degrees.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphics.Save">
      <summary>
        <para>Saves the current state of the DXGraphics object.</para>
      </summary>
      <returns>The saved state.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphics.ScaleTransform(System.Single,System.Single,DevExpress.Drawing.DXMatrixOrder)">
      <summary>
        <para>Scales the coordinate system by the specified scale factor. Allows you to set the operation order.</para>
      </summary>
      <param name="sx">Scale factor in the x direction.</param>
      <param name="sy">Scale factor in the y direction.</param>
      <param name="order">An enumeration value that indicates the operation order.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphics.ScaleTransform(System.Single,System.Single)">
      <summary>
        <para>Scales the coordinate system by the specified scale factor.</para>
      </summary>
      <param name="sx">Scale factor in the x direction.</param>
      <param name="sy">Scale factor in the y direction.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphics.SetClip(DevExpress.Drawing.DXGraphicsPath,DevExpress.Drawing.DXClipCombineMode)">
      <summary>
        <para>Performs the specified combining operation with the current clip region and a DXGraphicsPath object and sets the clipping rerion to the operation result.</para>
      </summary>
      <param name="path">A path to combine.</param>
      <param name="combineMode">An enumeration that specifies the combining operation to use.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphics.SetClip(DevExpress.Drawing.DXGraphicsPath)">
      <summary>
        <para>Sets the clipping region of to the specified DXGraphicsPath object.</para>
      </summary>
      <param name="path">A new clipping region.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphics.SetClip(DevExpress.Drawing.DXRegion,DevExpress.Drawing.DXClipCombineMode)">
      <summary>
        <para>Performs the specified combining operation with the current clip region and another region and sets the clipping region to the operation result.</para>
      </summary>
      <param name="region">A region to combine.</param>
      <param name="combineMode">An enumeration that specifies the combining operation to use.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphics.SetClip(DevExpress.Drawing.DXRegion)">
      <summary>
        <para>Sets the clipping region to the specified region.</para>
      </summary>
      <param name="region">The new clipping region.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphics.SetClip(System.Drawing.RectangleF,DevExpress.Drawing.DXClipCombineMode)">
      <summary>
        <para>Performs the specified combining operation with the current clip region and a rectangle and sets the clipping region to the operation result.</para>
      </summary>
      <param name="rect">A rectangle to combine.</param>
      <param name="combineMode">An enumeration that specifies the combining operation to use.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphics.SetClip(System.Drawing.RectangleF)">
      <summary>
        <para>Sets the clipping region to the specified rectangle.</para>
      </summary>
      <param name="rect">A new clipping region.</param>
    </member>
    <member name="P:DevExpress.Drawing.DXGraphics.SmoothingMode">
      <summary>
        <para>Gets or sets the rendering quality.</para>
      </summary>
      <value>An enumeration value that indicates whether lines, curves, and the edges of filled areas use smoothing (also called antialiasing).</value>
    </member>
    <member name="P:DevExpress.Drawing.DXGraphics.TextRenderingHint">
      <summary>
        <para>Gets or sets the text rendering mode.</para>
      </summary>
      <value>An enumeration value that indicates whether text renders with antialiasing.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXGraphics.Transform">
      <summary>
        <para>Gets or sets a copy of the geometric world transformation.</para>
      </summary>
      <value>A copy of the DXMatrix that is the geometric world transformation.</value>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphics.TranslateTransform(System.Single,System.Single,DevExpress.Drawing.DXMatrixOrder)">
      <summary>
        <para>Translates the coordinate system origin to the specified point.</para>
      </summary>
      <param name="dx">The x-coordinate of the translation.</param>
      <param name="dy">The y-coordinate of the translation.</param>
      <param name="order">An enumeration value that indicates the operation order.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphics.TranslateTransform(System.Single,System.Single)">
      <summary>
        <para>Translates the coordinate system origin to the specified point.</para>
      </summary>
      <param name="dx">The x-coordinate of the translation.</param>
      <param name="dy">The y-coordinate of the translation.</param>
    </member>
    <member name="T:DevExpress.Drawing.DXGraphicsPath">
      <summary>
        <para>Defines a series of connected lines and curves.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphicsPath.#ctor(DevExpress.Drawing.DXFillMode)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Drawing.DXGraphicsPath"/> class with specified settings.</para>
      </summary>
      <param name="fillMode">An enumeration value that indicates the fill mode of the closed path interior.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphicsPath.#ctor(DevExpress.Drawing.DXGraphicsPathData)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Drawing.DXGraphicsPath"/> class with specified settings.</para>
      </summary>
      <param name="pathData">Graphics data (a list of points and types) to define the DXGraphicsPath instance.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphicsPath.#ctor(System.Collections.Generic.IReadOnlyList{System.Drawing.PointF},System.Collections.Generic.IReadOnlyList{DevExpress.Drawing.DXPathPointType},DevExpress.Drawing.DXFillMode)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Drawing.DXGraphicsPath"/> class with specified settings.</para>
      </summary>
      <param name="points">A list of point coordinates that define the DXGraphicsPath instance.</param>
      <param name="types">A list of enumeration values that specify the type of each corresponding point in the points list.</param>
      <param name="fillMode">An enumeration value that indicates the fill mode of the closed path interior.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphicsPath.AddArc(System.Drawing.RectangleF,System.Single,System.Single)">
      <summary>
        <para>Appends an elliptical arc to the current figure.</para>
      </summary>
      <param name="rectangle">The rectangular bounds of the ellipse from which the arc is taken.</param>
      <param name="startAngle">The starting angle of the arc, measured in degrees clockwise from the x-axis.</param>
      <param name="sweepAngle">The angle between startAngle and the end of the arc.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphicsPath.AddArc(System.Single,System.Single,System.Single,System.Single,System.Single,System.Single)">
      <summary>
        <para>Appends an elliptical arc to the current figure.</para>
      </summary>
      <param name="x">The x-coordinate of the rectangle’s upper-left corner. The rectangle defines the arc ellipse.</param>
      <param name="y">The y-coordinate of the rectangle’s upper-left corner. The rectangle defines the arc ellipse.</param>
      <param name="width">The width of the rectangle. The rectangle defines the ellipse from which the arc is drawn.</param>
      <param name="height">The height of the rectangle. The rectangle defines the ellipse from which the arc is drawn.</param>
      <param name="startAngle">The starting angle of the arc, measured in degrees clockwise from the x-axis.</param>
      <param name="sweepAngle">The angle between startAngle and the end of the arc.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphicsPath.AddBezier(System.Drawing.PointF,System.Drawing.PointF,System.Drawing.PointF,System.Drawing.PointF)">
      <summary>
        <para>Adds a cubic Bezier curve to the current figure.</para>
      </summary>
      <param name="startPoint">The curve’s starting point.</param>
      <param name="controlPoint1">The curve’s first control point.</param>
      <param name="controlPoint2">The curve’s second control point.</param>
      <param name="endPoint">The curve’s endpoint.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphicsPath.AddBezier(System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single)">
      <summary>
        <para>Adds a cubic Bezier curve to the current figure.</para>
      </summary>
      <param name="x1">The x-coordinate of the curve’s starting point.</param>
      <param name="y1">The y-coordinate of the curve’s starting point.</param>
      <param name="x2">The x-coordinate of the curve’s first control point.</param>
      <param name="y2">The y-coordinate of the curve’s first control point.</param>
      <param name="x3">The x-coordinate of the curve’s second control point.</param>
      <param name="y3">The y-coordinate of the curve’s second control point.</param>
      <param name="x4">The x-coordinate of the curve’s ending point.</param>
      <param name="y4">The y-coordinate of the curve’s ending point.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphicsPath.AddBeziers(System.Collections.Generic.IReadOnlyList{System.Drawing.PointF})">
      <summary>
        <para>Adds a sequence of connected cubic Bezier curves to the current figure.</para>
      </summary>
      <param name="points">An list of points that define the curves.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphicsPath.AddCurve(System.Drawing.PointF[],System.Single)">
      <summary>
        <para>Adds a spline curve to the current figure.</para>
      </summary>
      <param name="points">An array of points that define the curve.</param>
      <param name="tension">Specifies the curve intensity. Values greater than 1 produce unpredictable results.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphicsPath.AddCurve(System.Drawing.PointF[])">
      <summary>
        <para>Adds a spline curve to the current figure.</para>
      </summary>
      <param name="points">An array of points that define the curve.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphicsPath.AddEllipse(System.Drawing.RectangleF)">
      <summary>
        <para>Adds an ellipse to the current path.</para>
      </summary>
      <param name="rect">The rectangle within which the ellipse should be drawn.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphicsPath.AddEllipse(System.Single,System.Single,System.Single,System.Single)">
      <summary>
        <para>Adds an ellipse to the current path.</para>
      </summary>
      <param name="x">The x-coordinate of the rectangle’s upper-left corner. The rectangle defines the ellipse.</param>
      <param name="y">The y-coordinate of the rectangle’s upper-left corner. The rectangle defines the ellipse.</param>
      <param name="width">The width of the rectangle that defines the ellipse.</param>
      <param name="height">The height of the rectangle that defines the ellipse.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphicsPath.AddLine(System.Drawing.PointF,System.Drawing.PointF)">
      <summary>
        <para>Appends a line segment to the figure.</para>
      </summary>
      <param name="startPoint">The line’s starting point.</param>
      <param name="endPoint">The line’s endpoint.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphicsPath.AddLine(System.Single,System.Single,System.Single,System.Single)">
      <summary>
        <para>Appends a line segment to the figure.</para>
      </summary>
      <param name="x1">The x-coordinate of the line’s starting point.</param>
      <param name="y1">The y-coordinate of the line’s starting point.</param>
      <param name="x2">The x-coordinate of the line’s endpoint.</param>
      <param name="y2">The y-coordinate of the line’s endpoint.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphicsPath.AddLines(System.Collections.Generic.IReadOnlyList{System.Drawing.PointF})">
      <summary>
        <para>Appends a line segment to the figure.</para>
      </summary>
      <param name="points">An array of points that define line segments to add.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphicsPath.AddPath(DevExpress.Drawing.DXGraphicsPath,System.Boolean)">
      <summary>
        <para>Appends a sequence of lines and curves to this path.</para>
      </summary>
      <param name="path">A path to add.</param>
      <param name="connect">true if the first figure in the added path is a part of the last figure in this path; false if the first figure in the added path is separate from the last figure in this path.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphicsPath.AddPie(System.Drawing.Rectangle,System.Single,System.Single)">
      <summary>
        <para>Adds the outline of a pie shape to this path.</para>
      </summary>
      <param name="rect">A bounding rectangle that defines the ellipse from which the pie is drawn.</param>
      <param name="startAngle">The starting angle for the pie section, measured in degrees clockwise from the x-axis.</param>
      <param name="sweepAngle">The angle between startAngle and the end of the pie section, measured in degrees clockwise from startAngle.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphicsPath.AddPie(System.Single,System.Single,System.Single,System.Single,System.Single,System.Single)">
      <summary>
        <para>Adds the outline of a pie shape to this path.</para>
      </summary>
      <param name="x">The x-coordinate of the rectangle’s upper-left corner. The rectangle defines the pie shape bounds.</param>
      <param name="y">The y-coordinate of the rectangle’s upper-left corner. The rectangle defines the pie shape bounds.</param>
      <param name="width">The width of the pie shape’s bounding rectangle.</param>
      <param name="height">The height of the pie shape’s bounding rectangle.</param>
      <param name="startAngle">The starting angle for the pie section, measured in degrees clockwise from the x-axis.</param>
      <param name="sweepAngle">The angle between startAngle and the end of the pie section, measured in degrees clockwise from startAngle.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphicsPath.AddPolygon(System.Collections.Generic.IReadOnlyList{System.Drawing.PointF})">
      <summary>
        <para>Adds a polygon to the figure.</para>
      </summary>
      <param name="points">A list of points that define the polygon.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphicsPath.AddRectangle(System.Drawing.RectangleF)">
      <summary>
        <para>Adds a rectangle to a figure.</para>
      </summary>
      <param name="rect">The rectangle to add.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphicsPath.AddRectangles(System.Collections.Generic.IReadOnlyList{System.Drawing.RectangleF})">
      <summary>
        <para>Adds a series of rectangles to this path.</para>
      </summary>
      <param name="rectangles">A list of rectangles to add.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphicsPath.AddString(System.String,DevExpress.Drawing.DXFont,System.Drawing.PointF,DevExpress.Drawing.DXStringFormat)">
      <summary>
        <para>Adds a text string to this path.</para>
      </summary>
      <param name="text">A text to add.</param>
      <param name="font">An object that contains font information.</param>
      <param name="point">The staring point of the text.</param>
      <param name="stringFormat">An object that contains text formatting information (alignment, line spacing and so on).</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphicsPath.AddString(System.String,DevExpress.Drawing.DXFont,System.Drawing.RectangleF,DevExpress.Drawing.DXStringFormat)">
      <summary>
        <para>Adds a text string to this path.</para>
      </summary>
      <param name="text">A text to add.</param>
      <param name="font">An object that contains font information.</param>
      <param name="rect">An area where to draw a text string.</param>
      <param name="stringFormat">An object that contains text formatting information (alignment, line spacing and so on).</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphicsPath.Clone">
      <summary>
        <para>Clones the DXGraphicsPath object.</para>
      </summary>
      <returns>A copy of the DXGraphicsPath object.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphicsPath.CloseFigure">
      <summary>
        <para>Closes the current figure and starts a new figure. If the current figure contains a sequence of connected lines and curves, the method closes the loop by connecting a line from the endpoint to the starting point.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphicsPath.Dispose">
      <summary>
        <para>Disposes of the DXGraphicsPath object.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Drawing.DXGraphicsPath.FillMode">
      <summary>
        <para>Gets or sets how to fill the interiors of shapes in the GraphicsPath instance.</para>
      </summary>
      <value>An enumeration value that indicates the shape interior’s fill mode.</value>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphicsPath.Flatten">
      <summary>
        <para>Converts each curve in the path into a sequence of connected line segments.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphicsPath.Flatten(DevExpress.Drawing.DXMatrix,System.Single)">
      <summary>
        <para>Applies the specified transform and converts each curve in this GraphicsPath into a sequence of connected line segments.</para>
      </summary>
      <param name="transform">A matrix by which to transform this GraphicsPath before flattening.</param>
      <param name="flatness">Specifies the maximum permitted error between the curve and its flattened approximation. A value of 0.25 is the default. Reducing the flatness value will increase the number of line segments in the approximation.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphicsPath.Flatten(DevExpress.Drawing.DXMatrix)">
      <summary>
        <para>Applies the specified transform and converts each curve in the GraphicsPath into a sequence of connected line segments.</para>
      </summary>
      <param name="transform">A matrix by which to transform this GraphicsPath before flattening.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphicsPath.GetBounds">
      <summary>
        <para>Returns bounds of this GraphicsPath instance.</para>
      </summary>
      <returns>A rectangle that bounds this GraphicsPath.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphicsPath.GetLastPoint">
      <summary>
        <para>Gets the last point in the <see cref="P:DevExpress.Drawing.DXGraphicsPath.PathPoints">PathPoints</see> array.</para>
      </summary>
      <returns>The last point in this path.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphicsPath.GetPathData">
      <summary>
        <para>Gets a path data (a list of points and types) for this GraphicsPath.</para>
      </summary>
      <returns>An object that contains path data.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphicsPath.IsVisible(System.Drawing.PointF)">
      <summary>
        <para>Indicates whether the DXGraphicsPath instance contains the specified point.</para>
      </summary>
      <param name="hitPoint">The point to test.</param>
      <returns>true if the DXGraphicsPath instance contains specified point; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphicsPath.IsVisible(System.Single,System.Single)">
      <summary>
        <para>Indicates whether the DXGraphicsPath instance contains the specified point.</para>
      </summary>
      <param name="x">The x-coordinate of the point to test.</param>
      <param name="y">The y-coordinate of the point to test.</param>
      <returns>true if the DXGraphicsPath instance contains specified point; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.Drawing.DXGraphicsPath.PathPoints">
      <summary>
        <para>Retrieves the path points.</para>
      </summary>
      <value>A list of points in the path.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXGraphicsPath.PathTypes">
      <summary>
        <para>Gets the types of the points in the <see cref="P:DevExpress.Drawing.DXGraphicsPath.PathPoints">PathPoints</see> list.</para>
      </summary>
      <value>A list of point types in the path.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXGraphicsPath.PointCount">
      <summary>
        <para>Gets the number of path points.</para>
      </summary>
      <value>The number of elements in the PathPoints or the PathTypes list.</value>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphicsPath.Reset">
      <summary>
        <para>Reverts properties of the current object to default values.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphicsPath.StartFigure">
      <summary>
        <para>Starts a new figure without closing the current figure. All subsequent points added to the path are added to this new figure.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphicsPath.Transform(DevExpress.Drawing.DXMatrix)">
      <summary>
        <para>Applies a transform matrix to this GraphicsPath.</para>
      </summary>
      <param name="matrix">The transformation matrix to apply.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphicsPath.Widen(DevExpress.Drawing.DXPen,DevExpress.Drawing.DXMatrix)">
      <summary>
        <para>Adds an additional outline to the DXGraphicsPath object.</para>
      </summary>
      <param name="pen">The width between the original outline of the path and the new outline this method creates.</param>
      <param name="matrix">A matrix that specifies a transform to apply to the path before widening.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphicsPath.Widen(DevExpress.Drawing.DXPen)">
      <summary>
        <para>Adds an additional outline to the path.</para>
      </summary>
      <param name="pen">The width between the original outline of the path and the new outline this method creates.</param>
    </member>
    <member name="T:DevExpress.Drawing.DXGraphicsPathData">
      <summary>
        <para>Contains graphical data</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphicsPathData.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Drawing.DXGraphicsPathData"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphicsPathData.#ctor(System.Collections.Generic.IReadOnlyList{System.Drawing.PointF},System.Collections.Generic.IReadOnlyList{DevExpress.Drawing.DXPathPointType},DevExpress.Drawing.DXFillMode)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Drawing.DXGraphicsPathData"/> class with specified settings.</para>
      </summary>
      <param name="points">A list of points used to construct the path.</param>
      <param name="types">A list of point types.</param>
      <param name="fillMode">An enumeration value that indicates the fill mode.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphicsPathData.#ctor(System.Collections.Generic.IReadOnlyList{System.Drawing.PointF},System.Collections.Generic.IReadOnlyList{DevExpress.Drawing.DXPathPointType})">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Drawing.DXGraphicsPathData"/> class with specified settings.</para>
      </summary>
      <param name="points">A list of points used to construct the path.</param>
      <param name="types">A list of point types.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphicsPathData.Clone">
      <summary>
        <para>Clones the current DXGraphicsPathData object.</para>
      </summary>
      <returns>A copy of the DXGraphicsPathData object.</returns>
    </member>
    <member name="P:DevExpress.Drawing.DXGraphicsPathData.FillMode">
      <summary>
        <para>Obtains a fill mode of the path interior.</para>
      </summary>
      <value>An enumeration value that indicates how to fill and clip the interior of a closed figure.</value>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphicsPathData.GetBounds">
      <summary>
        <para>Returns the bounds of the DXGraphicsPathData instance.</para>
      </summary>
      <returns>The DXGraphicsPathData bounds.</returns>
    </member>
    <member name="P:DevExpress.Drawing.DXGraphicsPathData.PathPoints">
      <summary>
        <para>Retrieves points used to construct the path.</para>
      </summary>
      <value>A list of points used to construct the path.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXGraphicsPathData.PathTypes">
      <summary>
        <para>Obtains types associated with the points in the path.</para>
      </summary>
      <value>A list of types associated with the points in the path.</value>
    </member>
    <member name="T:DevExpress.Drawing.DXGraphicsUnit">
      <summary>
        <para>Lists values that indicate the measurement unit for the given data.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXGraphicsUnit.Display">
      <summary>
        <para>The measurement units of the display device. Typically pixels for video displays, and 1/100 inch for printers.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXGraphicsUnit.Document">
      <summary>
        <para>The document unit (1/300 inch).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXGraphicsUnit.Inch">
      <summary>
        <para>The inch.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXGraphicsUnit.Millimeter">
      <summary>
        <para>The millimeter.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXGraphicsUnit.Pixel">
      <summary>
        <para>The device pixel.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXGraphicsUnit.Point">
      <summary>
        <para>The printer’s point (1/72 inch).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXGraphicsUnit.World">
      <summary>
        <para>The world coordinate system unit.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Drawing.DXGraphicsUnitConverter">
      <summary>
        <para>Enables you to convert values that are calculated in some measure units to different measure units.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphicsUnitConverter.Convert(System.Drawing.SizeF,DevExpress.Drawing.DXGraphicsUnit,DevExpress.Drawing.DXGraphicsUnit)">
      <summary>
        <para>Adjusts the document margins calculated for the specified unit of measure according to a new unit of measure.</para>
      </summary>
      <param name="size">The size to convert.</param>
      <param name="fromUnit">The initial unit of measure.</param>
      <param name="toUnit">The required unit of measure.</param>
      <returns>The conversion result.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphicsUnitConverter.Convert(System.Drawing.SizeF,System.Single,System.Single)">
      <summary>
        <para>Adjusts the document margins calculated for the specified DPI according to a new DPI.</para>
      </summary>
      <param name="size">A size to convert.</param>
      <param name="fromDpi">The initial DPI setting.</param>
      <param name="toDpi">The required DPI setting.</param>
      <returns>The conversion result.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphicsUnitConverter.Convert(System.Int32,System.Single,System.Single)">
      <summary>
        <para>Adjusts the integer value calculated for the specified DPI according to a new DPI.</para>
      </summary>
      <param name="val">The value to convert.</param>
      <param name="fromDpi">The initial DPI setting.</param>
      <param name="toDpi">The required DPI setting.</param>
      <returns>The conversion result.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphicsUnitConverter.Convert(System.Single,DevExpress.Drawing.DXGraphicsUnit,DevExpress.Drawing.DXGraphicsUnit)">
      <summary>
        <para>Adjusts the document margins calculated for the specified unit of measure according to a new unit of measure.</para>
      </summary>
      <param name="val">The value to convert.</param>
      <param name="fromUnit">The initial unit of measure.</param>
      <param name="toUnit">The required unit of measure.</param>
      <returns>The conversion result.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXGraphicsUnitConverter.Convert(System.Single,System.Single,System.Single)">
      <summary>
        <para>Adjusts the float value calculated for the specified DPI according to a new DPI.</para>
      </summary>
      <param name="val">The value to convert.</param>
      <param name="fromDpi">The initial DPI setting.</param>
      <param name="toDpi">The required DPI setting.</param>
      <returns>The conversion result.</returns>
    </member>
    <member name="T:DevExpress.Drawing.DXHatchBrush">
      <summary>
        <para>A rectangular brush with a hatch style, a foreground color, and a background color.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.DXHatchBrush.#ctor(DevExpress.Drawing.DXHatchStyle,System.Drawing.Color,System.Drawing.Color)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Drawing.DXHatchBrush"/> class with specified settings.</para>
      </summary>
      <param name="hatchStyle">An enumeration value that indicates the the pattern that the hatch brush should draw.</param>
      <param name="foregroundColor">The brush’s foreground color.</param>
      <param name="backgroundColor">The brush’s background color.</param>
    </member>
    <member name="P:DevExpress.Drawing.DXHatchBrush.BackgroundColor">
      <summary>
        <para>Obtains the background color of the brush.</para>
      </summary>
      <value>The brush’s background color.</value>
    </member>
    <member name="M:DevExpress.Drawing.DXHatchBrush.Clone">
      <summary>
        <para>Clones the current DXHatchBrush instance.</para>
      </summary>
      <returns>A copy of the current object.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXHatchBrush.Equals(System.Object)">
      <summary>
        <para>Determines whether the specified object is equal to the current DXHatchBrush object.</para>
      </summary>
      <param name="obj">An object to compare with the current DXHatchBrush object.</param>
      <returns>true if the specified object is equal to the current object; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.Drawing.DXHatchBrush.ForegroundColor">
      <summary>
        <para>Retrieves the foreground color of the brush.</para>
      </summary>
      <value>The brush’s foreground color.</value>
    </member>
    <member name="M:DevExpress.Drawing.DXHatchBrush.GetHashCode">
      <summary>
        <para>Returns the hash code for the current DXHatchBrush object.</para>
      </summary>
      <returns>The hash code for the current object.</returns>
    </member>
    <member name="P:DevExpress.Drawing.DXHatchBrush.HatchStyle">
      <summary>
        <para>Obtains the hatch style of a brush.</para>
      </summary>
      <value>An enumeration value that indicates the hatch style.</value>
    </member>
    <member name="T:DevExpress.Drawing.DXHatchStyle">
      <summary>
        <para>Lists values that indicate patterns available for a hatch brush.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.BackwardDiagonal">
      <summary>
        <para>A pattern of lines on a diagonal from upper right to lower left edges.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.Cross">
      <summary>
        <para>Cross lines.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.DarkDownwardDiagonal">
      <summary>
        <para>Diagonal lines that slant to the right from top points to bottom points are spaced 50 percent closer and twice the width of ForwardDiagonal. This hatch pattern is not antialiased.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.DarkHorizontal">
      <summary>
        <para>Horizontal lines that are spaced 50 percent closer than Horizontal and twice the width of Horizontal.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.DarkUpwardDiagonal">
      <summary>
        <para>Diagonal lines that slant to the left from top points to bottom points are spaced 50 percent closer and twice the width of BackwardDiagonal, but the lines are not antialiased.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.DarkVertical">
      <summary>
        <para>Vertical lines that are spaced 50 percent closer than Vertical and twice its width.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.DashedDownwardDiagonal">
      <summary>
        <para>Dashed diagonal lines that slant to the right from top points to bottom points.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.DashedHorizontal">
      <summary>
        <para>Dashed horizontal lines.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.DashedUpwardDiagonal">
      <summary>
        <para>Dashed diagonal lines that slant to the left from top points to bottom points.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.DashedVertical">
      <summary>
        <para>Dashed vertical lines.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.DiagonalBrick">
      <summary>
        <para>Layered bricks that slant to the left from top points to bottom points.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.DiagonalCross">
      <summary>
        <para>Crisscross diagonal lines.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.Divot">
      <summary>
        <para>Divot pattern.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.DottedDiamond">
      <summary>
        <para>Forward diagonal and backward diagonal crosses lines, each of which is composed of dots.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.DottedGrid">
      <summary>
        <para>Horizontal and vertical crossed lines, each of which is composed of dots.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.ForwardDiagonal">
      <summary>
        <para>A pattern of lines on a diagonal from upper left to lower right.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.Horizontal">
      <summary>
        <para>A pattern of horizontal lines.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.HorizontalBrick">
      <summary>
        <para>Horizontally layered bricks.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.LargeCheckerBoard">
      <summary>
        <para>A checkerboard with squares that are twice the size of SmallCheckerBoard.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.LargeConfetti">
      <summary>
        <para>A confetti composed of larger pieces than SmallConfetti.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.LargeGrid">
      <summary>
        <para>Crosses lines pattern.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.LightDownwardDiagonal">
      <summary>
        <para>Diagonal lines that slant to the right from top points to bottom points and are spaced 50 percent closer together than ForwardDiagonal, but are not antialiased.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.LightHorizontal">
      <summary>
        <para>Horizontal lines that are spaced 50 percent closer together than Horizontal.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.LightUpwardDiagonal">
      <summary>
        <para>Diagonal lines that slant to the left from top points to bottom points and are spaced 50 percent closer together than BackwardDiagonal, but they are not antialiased.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.LightVertical">
      <summary>
        <para>Vertical lines that are spaced 50 percent closer together than Vertical.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.Max">
      <summary>
        <para>Specifies the LargeGrid hatch style.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.Min">
      <summary>
        <para>Specifies the Horizontal hatch style.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.NarrowHorizontal">
      <summary>
        <para>Horizontal lines that are spaced 75 percent closer together than hatch style Horizontal (or 25 percent closer together than LightHorizontal).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.NarrowVertical">
      <summary>
        <para>Vertical lines that are spaced 75 percent closer together than hatch style Vertical (or 25 percent closer together than LightVertical).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.OutlinedDiamond">
      <summary>
        <para>Forward diagonal and backward diagonal lines that cross but are not antialiased.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.Percent05">
      <summary>
        <para>A 5-percent hatch. The ratio of foreground color to background color is 5:95.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.Percent10">
      <summary>
        <para>A 10-percent hatch. The ratio of foreground color to background color is 10:90</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.Percent20">
      <summary>
        <para>A 20-percent hatch. The ratio of foreground color to background color is 20:80.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.Percent25">
      <summary>
        <para>A 25-percent hatch. The ratio of foreground color to background color is 25:75.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.Percent30">
      <summary>
        <para>A 30-percent hatch. The ratio of foreground color to background color is 30:70.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.Percent40">
      <summary>
        <para>A 40-percent hatch. The ratio of foreground color to background color is 40:60.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.Percent50">
      <summary>
        <para>A 50-percent hatch. The ratio of foreground color to background color is 50:50.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.Percent60">
      <summary>
        <para>A 60-percent hatch. The ratio of foreground color to background color is 60:40.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.Percent70">
      <summary>
        <para>A 70-percent hatch. The ratio of foreground color to background color is 70:30.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.Percent75">
      <summary>
        <para>A 75-percent hatch. The ratio of foreground color to background color is 75:25.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.Percent80">
      <summary>
        <para>A 80-percent hatch. The ratio of foreground color to background color is 80:100.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.Percent90">
      <summary>
        <para>A 90-percent hatch. The ratio of foreground color to background color is 90:10.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.Plaid">
      <summary>
        <para>A plaid material pattern.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.Shingle">
      <summary>
        <para>Diagonally layered shingles that slant to the right from top points to bottom points.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.SmallCheckerBoard">
      <summary>
        <para>A checkerboard pattern.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.SmallConfetti">
      <summary>
        <para>A confetti pattern.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.SmallGrid">
      <summary>
        <para>Horizontal and vertical lines that cross and are spaced 50 percent closer together than hatch style Cross.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.SolidDiamond">
      <summary>
        <para>A diagonally placed checkerboard.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.Sphere">
      <summary>
        <para>Spheres laid adjacent to one another.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.Trellis">
      <summary>
        <para>Trellis pattern.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.Vertical">
      <summary>
        <para>A pattern of vertical lines.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.Wave">
      <summary>
        <para>Horizontal lines that are composed of tildes.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.Weave">
      <summary>
        <para>A woven material pattern.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.WideDownwardDiagonal">
      <summary>
        <para>Diagonal lines that slant to the right from top points to bottom points, have the same spacing as hatch style ForwardDiagonal, and are triple its width, but are not antialiased.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.WideUpwardDiagonal">
      <summary>
        <para>Diagonal lines that slant to the left from top points to bottom points, have the same spacing as hatch style BackwardDiagonal, and are triple its width, but are not antialiased.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHatchStyle.ZigZag">
      <summary>
        <para>Horizontal lines that are composed of zigzags.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Drawing.DXHotkeyPrefix">
      <summary>
        <para>Lists values that indicate the display mode of the hotkey prefixes.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHotkeyPrefix.Hide">
      <summary>
        <para>Do not display the hotkey prefix.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHotkeyPrefix.None">
      <summary>
        <para>No hotkey prefix.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXHotkeyPrefix.Show">
      <summary>
        <para>Display the hot-key prefix.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Drawing.DXImage">
      <summary>
        <para>Base class for images in DevExpress controls.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.DXImage.Clone">
      <summary>
        <para>Creates a copy of the <see cref="T:DevExpress.Drawing.DXImage"/> instance.</para>
      </summary>
      <returns>The copy of the <see cref="T:DevExpress.Drawing.DXImage"/> instance.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXImage.Dispose">
      <summary>
        <para>Releases all resources used by the <see cref="T:DevExpress.Drawing.DXImage"/> object.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.DXImage.Equals(DevExpress.Drawing.DXImage)">
      <summary>
        <para>Determines whether the specified object is equal to the current DXImage instance.</para>
      </summary>
      <param name="other">A DXImage object to test.</param>
      <returns>true if the DXImage parameter has the same property values as the current DXImage instance; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXImage.FromBase64String(System.String)">
      <summary>
        <para>Creates an image from the specified binary string that contains the image data.</para>
      </summary>
      <param name="base64">A binary string that contains image data.</param>
      <returns>The created image.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXImage.FromStream(System.IO.Stream)">
      <summary>
        <para>Creates an image from the specified stream that contains the image data.</para>
      </summary>
      <param name="stream">A stream that contains data for the image.</param>
      <returns>The created image.</returns>
    </member>
    <member name="P:DevExpress.Drawing.DXImage.Height">
      <summary>
        <para>Gets the height of the image in pixels.</para>
      </summary>
      <value>The height of the image in pixels.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXImage.HorizontalResolution">
      <summary>
        <para>Gets the horizontal resolution of the image in pixels per inch.</para>
      </summary>
      <value>The horizontal resolution of the image in pixels per inch.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXImage.ImageFormat">
      <summary>
        <para>Gets the image format.</para>
      </summary>
      <value>The image format.</value>
    </member>
    <member name="M:DevExpress.Drawing.DXImage.RotateFlip(DevExpress.Drawing.DXRotateFlipType)">
      <summary>
        <para>Rotates and flips an image.</para>
      </summary>
      <param name="rotateFlipType">An enumeration value that indicates the rotation and flip type.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXImage.Save(System.IO.Stream,DevExpress.Drawing.DXImageFormat)">
      <summary>
        <para>Saves the image to the specified stream in the specified format.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object to which the image is saved.</param>
      <param name="imageFormat">The specified format for the image.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXImage.Save(System.IO.Stream)">
      <summary>
        <para>Saves the image to the specified stream.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object to which the image is saved.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXImage.Save(System.String,DevExpress.Drawing.DXImageFormat)">
      <summary>
        <para>Saves the image to the specified file in the specified format.</para>
      </summary>
      <param name="fileName">The name of the file to which the image is saved.</param>
      <param name="imageFormat">The specified format for the image.</param>
    </member>
    <member name="P:DevExpress.Drawing.DXImage.Size">
      <summary>
        <para>Gets the width and height of the image in pixels.</para>
      </summary>
      <value>The width and height of the image in pixels.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXImage.VerticalResolution">
      <summary>
        <para>Gets the vertical resolution of the image in pixels per inch.</para>
      </summary>
      <value>The vertical resolution of the image in pixels.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXImage.Width">
      <summary>
        <para>Gets the width of the image in pixels.</para>
      </summary>
      <value>The width of the image in pixels.</value>
    </member>
    <member name="T:DevExpress.Drawing.DXImageFormat">
      <summary>
        <para>Specifies the image format.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.DXImageFormat.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Drawing.DXImageFormat"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Drawing.DXImageFormat.Bmp">
      <summary>
        <para>Gets the bitmap (BMP) image format.</para>
      </summary>
      <value>An object that indicates the bitmap image format.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXImageFormat.Emf">
      <summary>
        <para>Gets the enhanced metafile (EMF) image format. Currently not supported.</para>
      </summary>
      <value>An object that indicates the enhanced metafile image format.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXImageFormat.Gif">
      <summary>
        <para>Gets the Graphics Interchange Format (GIF) image format.</para>
      </summary>
      <value>An object that indicates the GIF image format.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXImageFormat.Icon">
      <summary>
        <para>Gets the Windows icon (.ico) image format.</para>
      </summary>
      <value>An object that indicates the Windows icon image format.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXImageFormat.Jpeg">
      <summary>
        <para>Gets the Joint Photographic Experts Group (JPEG) image format.</para>
      </summary>
      <value>An object that indicates the JPEG image format.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXImageFormat.Png">
      <summary>
        <para>Gets the W3C Portable Network Graphics (PNG) image format.</para>
      </summary>
      <value>An object that indicates the PNG image format.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXImageFormat.Svg">
      <summary>
        <para>Gets the Scalable Vector Graphics (SVG) image format.</para>
      </summary>
      <value>An object that indicates the SVG image format.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXImageFormat.Tiff">
      <summary>
        <para>Gets the Tagged Image File Format (TIFF) image format.</para>
      </summary>
      <value>An object that indicates the TIFF image format.</value>
    </member>
    <member name="M:DevExpress.Drawing.DXImageFormat.ToString">
      <summary>
        <para>Converts this DXImageFormat object to a string.</para>
      </summary>
      <returns>A string that is this DXImageFormat object.</returns>
    </member>
    <member name="P:DevExpress.Drawing.DXImageFormat.Unknown">
      <summary>
        <para>Gets the unknown image format.</para>
      </summary>
      <value>An object that indicates the unknown image format.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXImageFormat.Wmf">
      <summary>
        <para>Gets the Windows metafile (WMF) image format. Currently not supported.</para>
      </summary>
      <value>An object that indicates the Windows metafile image format.</value>
    </member>
    <member name="T:DevExpress.Drawing.DXInterpolationMode">
      <summary>
        <para>Lists values that indicate the algorithm used when images are scaled or rotated.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXInterpolationMode.Bicubic">
      <summary>
        <para>Bicubic interpolation. No pre-filter. This mode is not suitable for shrinking an image below 25 percent of its original size.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXInterpolationMode.Bilinear">
      <summary>
        <para>Bilinear interpolation. No pre-filter. This mode is not suitable for shrinking an image below 50 percent of its original size.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXInterpolationMode.Default">
      <summary>
        <para>Default mode.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXInterpolationMode.High">
      <summary>
        <para>High quality interpolation.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXInterpolationMode.HighQualityBicubic">
      <summary>
        <para>High-quality bicubic interpolation. Pre-filter is applied to ensure high-quality shrinking. This mode produces the highest quality transformed images.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXInterpolationMode.HighQualityBilinear">
      <summary>
        <para>High-quality, bilinear interpolation. Pre-filter is applied to ensure high-quality shrinking.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXInterpolationMode.Invalid">
      <summary>
        <para>Invalid mode.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXInterpolationMode.Low">
      <summary>
        <para>Low quality interpolation.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXInterpolationMode.NearestNeighbor">
      <summary>
        <para>Nearest-neighbor interpolation.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Drawing.DXLinearGradientBrush">
      <summary>
        <para>A brush that paints an area with a linear gradient.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.DXLinearGradientBrush.#ctor(System.Drawing.PointF,System.Drawing.PointF,System.Drawing.Color,System.Drawing.Color)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Drawing.DXLinearGradientBrush"/> class with specified settings.</para>
      </summary>
      <param name="startPoint">The starting point of the linear gradient.</param>
      <param name="endPoint">The ending point of the linear gradient.</param>
      <param name="color1">The starting color of the gradient.</param>
      <param name="color2">The ending color of the gradient.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXLinearGradientBrush.#ctor(System.Drawing.RectangleF,System.Drawing.Color,System.Drawing.Color,DevExpress.Drawing.DXLinearGradientMode)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Drawing.DXLinearGradientBrush"/> class with specified settings.</para>
      </summary>
      <param name="rectangle">The bounds of the linear gradient.</param>
      <param name="color1">The starting color of the gradient.</param>
      <param name="color2">The ending color of the gradient.</param>
      <param name="linearGradientMode">An enumeration value that specifies the orientation of the gradient.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXLinearGradientBrush.#ctor(System.Drawing.RectangleF,System.Drawing.Color,System.Drawing.Color,System.Single,System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Drawing.DXLinearGradientBrush"/> class with specified settings.</para>
      </summary>
      <param name="rectangle">The bounds of the linear gradient.</param>
      <param name="color1">The starting color of the gradient.</param>
      <param name="color2">The ending color of the gradient.</param>
      <param name="angle">The angle of the gradient’s orientation line, measured in degrees clockwise from the x-axis.</param>
      <param name="isAngleScalable">true if the angle is affected by the transformation matrix associated with this DXLinearGradientBrush; otherwise, false.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXLinearGradientBrush.#ctor(System.Drawing.RectangleF,System.Drawing.Color,System.Drawing.Color)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Drawing.DXLinearGradientBrush"/> class with specified settings.</para>
      </summary>
      <param name="rectangle">The bounds of the linear gradient.</param>
      <param name="color1">The starting color of the gradient.</param>
      <param name="color2">The ending color of the gradient.</param>
    </member>
    <member name="P:DevExpress.Drawing.DXLinearGradientBrush.Blend">
      <summary>
        <para>Gets or sets a custom falloff of the gradient.</para>
      </summary>
      <value>An object that contains arrays of blend factors and positions.</value>
    </member>
    <member name="M:DevExpress.Drawing.DXLinearGradientBrush.Clone">
      <summary>
        <para>Clones the current DXLinearGradientBrush object.</para>
      </summary>
      <returns>A copy of the DXLinearGradientBrush object.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXLinearGradientBrush.Equals(System.Object)">
      <summary>
        <para>Determines whether the specified object is equal to the current DXLinearGradientBrush object.</para>
      </summary>
      <param name="obj">An object to compare with the current DXLinearGradientBrush object.</param>
      <returns>true if the specified object is equal to the current object; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXLinearGradientBrush.GetHashCode">
      <summary>
        <para>Returns the hash code for the current DXLinearGradientBrush object.</para>
      </summary>
      <returns>The hash code for the current object.</returns>
    </member>
    <member name="P:DevExpress.Drawing.DXLinearGradientBrush.InterpolationColors">
      <summary>
        <para>Gets or sets a multicolor linear gradient.</para>
      </summary>
      <value>An object that defines a multicolor linear gradient.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXLinearGradientBrush.LinearColors">
      <summary>
        <para>Gets or sets the starting and ending colors of the gradient.</para>
      </summary>
      <value>An array of two Color structures that represents the starting and ending colors of the gradient.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXLinearGradientBrush.Rectangle">
      <summary>
        <para>Gets or sets a rectangular region that defines the starting and ending points of the gradient.</para>
      </summary>
      <value>A structure that specifies the starting and ending points of the gradient.</value>
    </member>
    <member name="M:DevExpress.Drawing.DXLinearGradientBrush.SetBlendTriangularShape(System.Single,System.Single)">
      <summary>
        <para>Creates a linear gradient with a center color and a linear falloff to a single color on both ends.</para>
      </summary>
      <param name="focus">A value (from 0 through 1) that specifies the point where the gradient is composed of only the ending color.</param>
      <param name="scale">A value (from 0 through 1) that specifies how fast the colors falloff from the starting color to focus (ending color)</param>
    </member>
    <member name="M:DevExpress.Drawing.DXLinearGradientBrush.SetBlendTriangularShape(System.Single)">
      <summary>
        <para>Creates a linear gradient with a center color and a linear falloff to a single color on both ends.</para>
      </summary>
      <param name="focus">A value (from 0 through 1) that specifies the  point where the gradient is composed of only the ending color.</param>
    </member>
    <member name="T:DevExpress.Drawing.DXLinearGradientMode">
      <summary>
        <para>Lists values that indicate the direction of a linear gradient.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXLinearGradientMode.BackwardDiagonal">
      <summary>
        <para>A gradient from upper right to lower left.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXLinearGradientMode.ForwardDiagonal">
      <summary>
        <para>A gradient from upper left to lower right.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXLinearGradientMode.Horizontal">
      <summary>
        <para>A gradient from left to right.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXLinearGradientMode.Vertical">
      <summary>
        <para>A gradient from top to bottom.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Drawing.DXLineCap">
      <summary>
        <para>Lists values that indicate available cap styles for a DXPen instance.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXLineCap.AnchorMask">
      <summary>
        <para>A mask used to check whether a line cap is an anchor cap.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXLineCap.ArrowAnchor">
      <summary>
        <para>An arrow-shaped anchor cap.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXLineCap.Custom">
      <summary>
        <para>A custom line cap.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXLineCap.DiamondAnchor">
      <summary>
        <para>A diamond anchor cap.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXLineCap.Flat">
      <summary>
        <para>A flat line cap.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXLineCap.NoAnchor">
      <summary>
        <para>No cap.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXLineCap.Round">
      <summary>
        <para>A round line cap.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXLineCap.RoundAnchor">
      <summary>
        <para>A round anchor cap.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXLineCap.Square">
      <summary>
        <para>A square line cap.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXLineCap.SquareAnchor">
      <summary>
        <para>A square anchor line cap.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXLineCap.Triangle">
      <summary>
        <para>A triangular line cap.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Drawing.DXLineJoin">
      <summary>
        <para>Lists values that indicate how to join consecutive line or curve segments in a figure.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXLineJoin.Bevel">
      <summary>
        <para>Specifies a beveled join. The join results in a diagonal corner.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXLineJoin.Miter">
      <summary>
        <para>Specifies a mitered join. The join results in a sharp corner or a clipped corner, depending on whether the length of the miter exceeds the miter limit.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXLineJoin.MiterClipped">
      <summary>
        <para>Specifies a mitered join. The join results in a sharp corner or a beveled corner, depending on whether the length of the miter exceeds the miter limit.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXLineJoin.Round">
      <summary>
        <para>Specifies a circular join. The join results in a smooth, circular arc between the lines.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Drawing.DXMargins">
      <summary>
        <para>Specifies the dimensions of the margins of a printed page.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.DXMargins.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Drawing.DXMargins"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.DXMargins.#ctor(System.Single,System.Single,System.Single,System.Single)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Drawing.DXMargins"/> class with specified settings.</para>
      </summary>
      <param name="left">The left margin.</param>
      <param name="right">The right margin.</param>
      <param name="top">The top margin.</param>
      <param name="bottom">The bottom margin.</param>
    </member>
    <member name="P:DevExpress.Drawing.DXMargins.Bottom">
      <summary>
        <para>Gets or sets the bottom margin.</para>
      </summary>
      <value>The bottom margin.</value>
    </member>
    <member name="M:DevExpress.Drawing.DXMargins.Clone">
      <summary>
        <para>Clones the current DXMargins object.</para>
      </summary>
      <returns>A copy of the current DXMargins object.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXMargins.Equals(DevExpress.Drawing.DXMargins)">
      <summary>
        <para>Determines whether the specified object is equal to the current DXMargins instance.</para>
      </summary>
      <param name="other">An object to compare.</param>
      <returns>true if the other parameter has the same property values as the current DXMargins instance; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXMargins.Equals(System.Object)">
      <summary>
        <para>Determines whether the specified object is equal to the current DXMargins instance.</para>
      </summary>
      <param name="obj">An object to compare.</param>
      <returns>true if the object parameter has the same property values as the current DXMargins instance; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXMargins.GetHashCode">
      <summary>
        <para>Gets the hash code for the DXMargins object.</para>
      </summary>
      <returns>The hash code.</returns>
    </member>
    <member name="P:DevExpress.Drawing.DXMargins.Left">
      <summary>
        <para>Gets or sets the left margin.</para>
      </summary>
      <value>The left margin.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXMargins.Right">
      <summary>
        <para>Gets or sets the right margin.</para>
      </summary>
      <value>The right margin.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXMargins.Top">
      <summary>
        <para>Gets or sets the top margin.</para>
      </summary>
      <value>The top margin.</value>
    </member>
    <member name="M:DevExpress.Drawing.DXMargins.ToString">
      <summary>
        <para>Returns a string that specifies `DXMargins’.</para>
      </summary>
      <returns>A string that specifies DXMargins.</returns>
    </member>
    <member name="T:DevExpress.Drawing.DXMatrix">
      <summary>
        <para>Encapsulates a 3x2 affine matrix used for geometric transformations.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.DXMatrix.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Drawing.DXMatrix"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.DXMatrix.#ctor(System.Single,System.Single,System.Single,System.Single,System.Single,System.Single)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Drawing.DXMatrix"/> class with specified elements.</para>
      </summary>
      <param name="m11">The value in the first row and first column.</param>
      <param name="m12">The value in the first row and second column.</param>
      <param name="m21">The value in the second row and first column.</param>
      <param name="m22">The value in the second row and second column.</param>
      <param name="dx">The value in the third row and first column.</param>
      <param name="dy">The value in the third row and second column.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXMatrix.#ctor(System.Single[])">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Drawing.DXMatrix"/> class with specified settings.</para>
      </summary>
      <param name="elements">An array of floating-point values that are the matrix elements.</param>
    </member>
    <member name="P:DevExpress.Drawing.DXMatrix.A">
      <summary>
        <para>Obtains the value in the first row and first column.</para>
      </summary>
      <value>The value in the first row and first column.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXMatrix.B">
      <summary>
        <para>Obtains the value in the first row and second column.</para>
      </summary>
      <value>The value in the first row and second column.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXMatrix.C">
      <summary>
        <para>Obtains the value in the second row and first column.</para>
      </summary>
      <value>The value in the second row and first column.</value>
    </member>
    <member name="M:DevExpress.Drawing.DXMatrix.Clone">
      <summary>
        <para>Clones the DXMatrix object.</para>
      </summary>
      <returns>A copy of the DXMatrix object.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXMatrix.CreateRotateMatrix(System.Single)">
      <summary>
        <para>Creates a new matrix based on the current matrix and applies a clockwise rotation of the specified angle relative to the matrix origin.</para>
      </summary>
      <param name="angle">Angle of rotation in degrees.’</param>
      <returns>The resulting transformation matrix.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXMatrix.CreateScaleMatrix(System.Single,System.Single)">
      <summary>
        <para>Creates a new matrix based on the current matrix and applies the specified scale vector.</para>
      </summary>
      <param name="scaleX">The value by which to scale in the x-axis direction.</param>
      <param name="scaleY">The value by which to scale in the y-axis direction.</param>
      <returns>The resulting transformation matrix.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXMatrix.CreateShearMatrix(System.Single,System.Single)">
      <summary>
        <para>Creates a new matrix based on the current matrix and applies the specified shear vector.</para>
      </summary>
      <param name="shearX">The horizontal shear factor.</param>
      <param name="shearY">The vertical shear factor.</param>
      <returns>The resulting transformation matrix.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXMatrix.CreateTranslateMatrix(System.Single,System.Single)">
      <summary>
        <para>Creates a new matrix based on the current matrix and translates the coordinate system origin to the specified offset.</para>
      </summary>
      <param name="offsetX">The x-coordinate of the translation.</param>
      <param name="offsetY">The y-coordinate of the translation.</param>
      <returns>The resulting transformation matrix.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXMatrix.CreateWarpMatrix(System.Drawing.RectangleF,System.Collections.Generic.IReadOnlyList{System.Drawing.PointF})">
      <summary>
        <para>Creates a new matrix based on the current matrix and applies a warp transform.</para>
      </summary>
      <param name="source">The rectangle that is transformed to the parallelogram defined by destination points.</param>
      <param name="destinationPoints">An array of points that define a parallelogram to which the source rectangle is transformed.</param>
      <returns>The resulting transformation matrix.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXMatrix.CreateWarpMatrix(System.Drawing.RectangleF,System.Drawing.RectangleF)">
      <summary>
        <para>Creates a new matrix based on the current matrix and applies a warp transformation.</para>
      </summary>
      <param name="src">The rectangle that changes into a parallelogram with the shape of the destination rectangle.</param>
      <param name="dst">A parallelogram to which the source rectangle is transformed.</param>
      <returns>The resulting transformation matrix.</returns>
    </member>
    <member name="P:DevExpress.Drawing.DXMatrix.D">
      <summary>
        <para>Obtains the value in the second row and second column.</para>
      </summary>
      <value>The value in the second row and second column.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXMatrix.E">
      <summary>
        <para>Obtains the value in the third row and first column.</para>
      </summary>
      <value>The value in the third row and first column.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXMatrix.Elements">
      <summary>
        <para>Obtains the matrix elements.’</para>
      </summary>
      <value>An array of matrix elements.</value>
    </member>
    <member name="M:DevExpress.Drawing.DXMatrix.Equals(DevExpress.Drawing.DXMatrix)">
      <summary>
        <para>Determines whether the specified object is equal to the current DXMatrix object.</para>
      </summary>
      <param name="matrix">A DXFont to compare with the current object.</param>
      <returns>true if the specified object is equal to the current object; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXMatrix.Equals(System.Object)">
      <summary>
        <para>Determines whether the specified object is equal to the current DXMatrix object.</para>
      </summary>
      <param name="obj">An object to compare with the current object.</param>
      <returns>true if the specified object is equal to the current object; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.Drawing.DXMatrix.F">
      <summary>
        <para>Obtains the value in the third row and second column.</para>
      </summary>
      <value>The value in the third row and second column.</value>
    </member>
    <member name="M:DevExpress.Drawing.DXMatrix.GetHashCode">
      <summary>
        <para>Returns the hash code for the current DXFont object.</para>
      </summary>
      <returns>The hash code for the current object.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXMatrix.Invert">
      <summary>
        <para>Inverts the DXMatrix instance (if it is invertible).</para>
      </summary>
    </member>
    <member name="P:DevExpress.Drawing.DXMatrix.IsIdentity">
      <summary>
        <para>Gets whether this DXMatrix is the identity matrix.</para>
      </summary>
      <value>true if the DXMatrix instance is the identity matrix; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXMatrix.IsInvertible">
      <summary>
        <para>Gets whether the DXMatrix object is invertible.</para>
      </summary>
      <value>true if the object is invertible; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.Drawing.DXMatrix.Multiply(DevExpress.Drawing.DXMatrix,DevExpress.Drawing.DXMatrixOrder)">
      <summary>
        <para>Multiplies this DXMatrix by the specified matrix, and in the order specified in the order parameter.</para>
      </summary>
      <param name="matrix">The matrix by which this DXMatrix is to be multiplied.</param>
      <param name="order">An enumeration value that indicates the order of the multiplication.</param>
    </member>
    <member name="P:DevExpress.Drawing.DXMatrix.OffsetX">
      <summary>
        <para>Gets the x translation value (the dx value, or the element in the third row and first column).</para>
      </summary>
      <value>The x translation value.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXMatrix.OffsetY">
      <summary>
        <para>Gets the y translation value (the dy value, or the element in the third row and second column).</para>
      </summary>
      <value>The y translation value.</value>
    </member>
    <member name="M:DevExpress.Drawing.DXMatrix.Reset">
      <summary>
        <para>Reverts the current object properties to default values.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.DXMatrix.Rotate(System.Single,DevExpress.Drawing.DXMatrixOrder)">
      <summary>
        <para>Applies a clockwise rotation to the specified angle around the origin (zero x and y coordinates).</para>
      </summary>
      <param name="angle">The angle (extent) of the rotation, in degrees.</param>
      <param name="order">An enumeration value that indicates the order (append or prepend) in which the rotation is applied.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXMatrix.RotateAt(System.Single,System.Drawing.PointF,DevExpress.Drawing.DXMatrixOrder)">
      <summary>
        <para>Applies a clockwise rotation around the specified point in the specified order.</para>
      </summary>
      <param name="angle">The angle of the rotation, in degrees.</param>
      <param name="point">The rotation center.</param>
      <param name="order">An enumeration value that indicates the order (append or prepend) in which the rotation is applied.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXMatrix.Scale(System.Single,System.Single,DevExpress.Drawing.DXMatrixOrder)">
      <summary>
        <para>Prepends and applies the specified scale vector to the DXMatrix instance.</para>
      </summary>
      <param name="scaleX">The value by which to scale DXMatrix in the x-axis direction.</param>
      <param name="scaleY">The value by which to scale DXMatrix in the y-axis direction.</param>
      <param name="order">An enumeration value that indicates the order (append or prepend) in which the scale vector is applied to this DXMatrix.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXMatrix.Shear(System.Single,System.Single,DevExpress.Drawing.DXMatrixOrder)">
      <summary>
        <para>Prepends and applies the specified shear vector to the DXMatrix instance.</para>
      </summary>
      <param name="shearX">The horizontal shear factor.</param>
      <param name="shearY">The vertical shear factor.</param>
      <param name="order">An enumeration value that indicates the order (append or prepend) in which the shear is applied.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXMatrix.TransformPoint(System.Drawing.PointF)">
      <summary>
        <para>Applies the geometric transformation to a point.</para>
      </summary>
      <param name="point">A point to transform.</param>
      <returns>The transformed point.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXMatrix.TransformPoints(System.Collections.Generic.IReadOnlyList{System.Drawing.PointF})">
      <summary>
        <para>Applies the geometric transformation to an array of points.</para>
      </summary>
      <param name="points">The points to transform.</param>
      <returns>The transformed points.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXMatrix.TransformPoints(System.Drawing.PointF[])">
      <summary>
        <para>Applies the geometric transformation to an array of points.</para>
      </summary>
      <param name="points">The points to transform.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXMatrix.Translate(System.Single,System.Single,DevExpress.Drawing.DXMatrixOrder)">
      <summary>
        <para>Applies the specified translation vector to DXMatrix in the specified order.</para>
      </summary>
      <param name="offsetX">The x-coordinate of the translation.</param>
      <param name="offsetY">The y-coordinate of the translation.</param>
      <param name="order">An enumeration value that indicates the order (append or prepend) in which the translation is applied.</param>
    </member>
    <member name="T:DevExpress.Drawing.DXMatrixOrder">
      <summary>
        <para>Lists values that indicate the order for matrix transformation operations.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXMatrixOrder.Append">
      <summary>
        <para>The new operation is applied after the old operation.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXMatrixOrder.Prepend">
      <summary>
        <para>The new operation is applied before the old operation.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Drawing.DXPathGradientBrush">
      <summary>
        <para>A brush that fills the interior of a DXGraphicsPath object with a gradient.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.DXPathGradientBrush.#ctor(DevExpress.Drawing.DXGraphicsPathData)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Drawing.DXPathGradientBrush"/> class with specified settings.</para>
      </summary>
      <param name="pathData">An object that defines the area which this PathGradientBrush should fill.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXPathGradientBrush.#ctor(DevExpress.Drawing.DXPathGradientBrush)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Drawing.DXPathGradientBrush"/> class with specified settings.</para>
      </summary>
      <param name="brush">The base gradient brush.</param>
    </member>
    <member name="P:DevExpress.Drawing.DXPathGradientBrush.Blend">
      <summary>
        <para>Gets or sets a custom falloff for the gradient.</para>
      </summary>
      <value>The custom falloff.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXPathGradientBrush.CenterColor">
      <summary>
        <para>Gets or sets the color at the center of the path gradient.</para>
      </summary>
      <value>The color at the center of the path gradient.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXPathGradientBrush.CenterPoint">
      <summary>
        <para>Gets or sets the center point of the path gradient.</para>
      </summary>
      <value>The center point of the path gradient.</value>
    </member>
    <member name="M:DevExpress.Drawing.DXPathGradientBrush.Clone">
      <summary>
        <para>Clones the current DXPathGradientBrush object.</para>
      </summary>
      <returns>A copy of the current DXPathGradientBrush object.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXPathGradientBrush.Equals(System.Object)">
      <summary>
        <para>Determines whether the specified object is equal to the current DXPathGradientBrush object.</para>
      </summary>
      <param name="obj">An object to compare with the current DXPathGradientBrush object.</param>
      <returns>true if the specified object is equal to the current object; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.Drawing.DXPathGradientBrush.FocusScales">
      <summary>
        <para>Gets or sets the focus point for the gradient falloff.</para>
      </summary>
      <value>The focus point for the gradient falloff.</value>
    </member>
    <member name="M:DevExpress.Drawing.DXPathGradientBrush.GetHashCode">
      <summary>
        <para>Returns the hash code for the current DXPathGradientBrush object.</para>
      </summary>
      <returns>The hash code for the current object.</returns>
    </member>
    <member name="P:DevExpress.Drawing.DXPathGradientBrush.InterpolationColors">
      <summary>
        <para>Gets or sets a multicolor linear gradient.</para>
      </summary>
      <value>An object defines a multicolor linear gradient.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXPathGradientBrush.PathData">
      <summary>
        <para>Obtains path data associated with the brush.</para>
      </summary>
      <value>An object that contains graphical data.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXPathGradientBrush.SurroundColors">
      <summary>
        <para>Gets or sets an array of colors that correspond to the points in the path the PathGradientBrush fills.</para>
      </summary>
      <value>An array of colors associated with each point in the path.</value>
    </member>
    <member name="T:DevExpress.Drawing.DXPathPointType">
      <summary>
        <para>List types of points available for the DXGraphicsPath instance.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXPathPointType.Bezier">
      <summary>
        <para>A Bezier curve.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXPathPointType.CloseSubpath">
      <summary>
        <para>The endpoint of a subpath.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXPathPointType.Line">
      <summary>
        <para>A line segment.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXPathPointType.PathTypeMask">
      <summary>
        <para>A mask point.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXPathPointType.Start">
      <summary>
        <para>The starting point of a DXGraphicsPath object.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Drawing.DXPen">
      <summary>
        <para>Defines an object used to draw lines and curves.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.DXPen.#ctor(DevExpress.Drawing.DXBrush,System.Single)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Drawing.DXPen"/> class with specified settings.</para>
      </summary>
      <param name="brush">A brush that defines the pen parameters.</param>
      <param name="width">The pen width.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXPen.#ctor(DevExpress.Drawing.DXBrush)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Drawing.DXPen"/> class with specified settings.</para>
      </summary>
      <param name="brush">A brush that defines the pen parameters.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXPen.#ctor(System.Drawing.Color,System.Single,DevExpress.Drawing.DXLineCap)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Drawing.DXPen"/> class with specified settings.</para>
      </summary>
      <param name="color">The pen color.</param>
      <param name="thickness">The pen width.</param>
      <param name="lineCap">An enumeration value that indicates the cap style applied to line ends.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXPen.#ctor(System.Drawing.Color,System.Single)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Drawing.DXPen"/> class with specified settings.</para>
      </summary>
      <param name="color">The pen color.</param>
      <param name="width">The pen width.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXPen.#ctor(System.Drawing.Color)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Drawing.DXPen"/> class with specified settings.</para>
      </summary>
      <param name="color">The pen color.</param>
    </member>
    <member name="P:DevExpress.Drawing.DXPen.Brush">
      <summary>
        <para>Gets or sets the brush that determines the pen attributes.</para>
      </summary>
      <value>A brush that determines the pen attributes.</value>
    </member>
    <member name="M:DevExpress.Drawing.DXPen.Clone">
      <summary>
        <para>Clones the current DXPen object.</para>
      </summary>
      <returns>A copy of the current DXPen object.</returns>
    </member>
    <member name="P:DevExpress.Drawing.DXPen.Color">
      <summary>
        <para>Gets the pen color.</para>
      </summary>
      <value>The pen color.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXPen.CustomEndCap">
      <summary>
        <para>Gets or sets a custom cap to use at the line end.</para>
      </summary>
      <value>An object the contains end cap settings.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXPen.CustomStartCap">
      <summary>
        <para>Gets or sets a custom cap to use at the line start.</para>
      </summary>
      <value>An object the contains end cap settings.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXPen.DashCap">
      <summary>
        <para>Gets or sets the cap style used at the dash end.</para>
      </summary>
      <value>An object that contains the dash style parameters.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXPen.DashOffset">
      <summary>
        <para>Gets or sets the distance between the start of a line to the start of a dash pattern.</para>
      </summary>
      <value>The distance between the start of a line to the beginning of a dash pattern.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXPen.DashPattern">
      <summary>
        <para>Gets or sets an array of custom dashes and spaces.</para>
      </summary>
      <value>An array of real numbers that specifies the lengths of alternating dashes and spaces in dashed lines.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXPen.DashStyle">
      <summary>
        <para>Gets or sets the style applied to dashed lines.</para>
      </summary>
      <value>An enumeration value that indicates the dash style.</value>
    </member>
    <member name="M:DevExpress.Drawing.DXPen.Dispose">
      <summary>
        <para>Disposes of the DXPen instance.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Drawing.DXPen.EndCap">
      <summary>
        <para>Gets or sets the cap style applied to the end of lines.</para>
      </summary>
      <value>The cap style applied to line ends.</value>
    </member>
    <member name="M:DevExpress.Drawing.DXPen.Equals(DevExpress.Drawing.DXPen)">
      <summary>
        <para>Determines whether the specified DXPen object is equal to the current DXPen instance.</para>
      </summary>
      <param name="other">An object to compare.</param>
      <returns>true if the other parameter is DXPen and has the same property values as the current DXPen instance; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXPen.Equals(System.Object)">
      <summary>
        <para>Determines whether the specified object is equal to the current DXPen instance.</para>
      </summary>
      <param name="obj">An object to compare.</param>
      <returns>true if the obj parameter has the same property values as the current DXPen instance; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXPen.GetHashCode">
      <summary>
        <para>Gets the hash code for the DXPen.</para>
      </summary>
      <returns>The hash code.</returns>
    </member>
    <member name="P:DevExpress.Drawing.DXPen.LineJoin">
      <summary>
        <para>Gets or sets the join style for the ends of two consecutive lines.</para>
      </summary>
      <value>An enumeration value that indicates the join style.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXPen.MiterLimit">
      <summary>
        <para>Gets or sets the limit of the thickness of the join on a mitered corner.</para>
      </summary>
      <value>The limit of the thickness of the join on a mitered corner.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXPen.PenType">
      <summary>
        <para>Obtains the type of lines the DXPen draws.</para>
      </summary>
      <value>An enumeration value that indicates the pen type.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXPen.StartCap">
      <summary>
        <para>Gets or sets the cap style applied to the line start.</para>
      </summary>
      <value>An enumeration value that indicate the start cap style.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXPen.Width">
      <summary>
        <para>Gets or sets the width of the pen.</para>
      </summary>
      <value>The pen width, measured in units specified by the <see cref="P:DevExpress.Drawing.DXGraphics.PageUnit">DXGraphics.PageUnit</see> property.</value>
    </member>
    <member name="T:DevExpress.Drawing.DXPenType">
      <summary>
        <para>Lists values that indicate the pen type.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXPenType.HatchFill">
      <summary>
        <para>The pen uses a hatch fill to fill lines.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXPenType.LinearGradient">
      <summary>
        <para>The pen uses a linear gradient fill to fill lines.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXPenType.PathGradient">
      <summary>
        <para>The pen uses a path gradient fill to fill lines.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXPenType.SolidColor">
      <summary>
        <para>The pen uses a solid color to fill lines.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXPenType.TextureFill">
      <summary>
        <para>The pen uses a bitmap texture fill to fill lines.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Drawing.DXRegion">
      <summary>
        <para>Describes the interior of a graphics shape composed of rectangles and paths.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.DXRegion.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Drawing.DXRegion"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.DXRegion.#ctor(DevExpress.Drawing.DXGraphicsPath)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Drawing.DXRegion"/> class with specified settings.</para>
      </summary>
      <param name="path">A DXGraphicsPath instance that defines a new region.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXRegion.#ctor(System.Drawing.RectangleF)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Drawing.DXRegion"/> class with specified settings.</para>
      </summary>
      <param name="rectangle">A rectangle that defines an area of a new region.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXRegion.Clone">
      <summary>
        <para>Clones the current DXRegion object.</para>
      </summary>
      <returns>A copy of the current DXRegion object.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXRegion.Complement(DevExpress.Drawing.DXGraphicsPath)">
      <summary>
        <para>Updates the region to contain the portion of the specified DXgraphicsPath that does not intersect with the current region.</para>
      </summary>
      <param name="path">A DXGraphicsPath instance to complement with the current region.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXRegion.Complement(DevExpress.Drawing.DXRegion)">
      <summary>
        <para>Updates the region to contain the portion of the specified DXRegion that does not intersect with the current region.</para>
      </summary>
      <param name="right">A region to complement with the current region.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXRegion.Complement(System.Drawing.RectangleF)">
      <summary>
        <para>Updates the region to contain the portion of the specified rectangle that does not intersect with the current region.</para>
      </summary>
      <param name="rect">A rectangle to complement with the current region.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXRegion.Dispose">
      <summary>
        <para>Disposes of the DXRegion object.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.DXRegion.Exclude(DevExpress.Drawing.DXGraphicsPath)">
      <summary>
        <para>Excludes the specified DXGraphicsPath instance from the region.</para>
      </summary>
      <param name="path">An object to exclude from the region.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXRegion.Exclude(DevExpress.Drawing.DXRegion)">
      <summary>
        <para>Excludes the specified region from the current region.</para>
      </summary>
      <param name="right">A region to exclude from the current region.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXRegion.Exclude(System.Drawing.RectangleF)">
      <summary>
        <para>Excludes the specified rectangle from the region.</para>
      </summary>
      <param name="rect">A rectangle to exclude from the current region.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXRegion.GetBounds">
      <summary>
        <para>Obtains the region bounds.</para>
      </summary>
      <returns>The region bounds.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXRegion.Intersect(DevExpress.Drawing.DXGraphicsPath)">
      <summary>
        <para>Assigns the clip region to the intersection of the current region and the specified DXGraphicsPath instance.</para>
      </summary>
      <param name="path">An object that intersects the current region.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXRegion.Intersect(DevExpress.Drawing.DXRegion)">
      <summary>
        <para>Assigns the clip region to the intersection of the current region and the specified region.</para>
      </summary>
      <param name="right">A region that intersects with the current region.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXRegion.Intersect(System.Drawing.RectangleF)">
      <summary>
        <para>Assigns the clip region to the intersection of the current region and the specified rectangle.</para>
      </summary>
      <param name="rect">A structure that intersects the current region.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXRegion.IsVisible(System.Drawing.PointF)">
      <summary>
        <para>Gets or sets whether the current region contains the specified point.</para>
      </summary>
      <param name="point">A point to check.</param>
      <returns>true if the region contains the point; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXRegion.IsVisible(System.Drawing.Rectangle)">
      <summary>
        <para>Gets or sets whether the current region contains the specified rectangle.</para>
      </summary>
      <param name="rect">A rectangle to check.</param>
      <returns>true if the region contains the rectangle; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXRegion.MakeEmpty">
      <summary>
        <para>Initializes the region to an empty interior.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.DXRegion.MakeInfinite">
      <summary>
        <para>Initializes the region to an infinite interior.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.DXRegion.Transform(DevExpress.Drawing.DXMatrix)">
      <summary>
        <para>Transforms the region by the specified transformation matrix.</para>
      </summary>
      <param name="transform">The matrix by which to transform the region.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXRegion.Union(DevExpress.Drawing.DXGraphicsPath)">
      <summary>
        <para>Updates the region to the union of itself and the specified DXGraphicsPath instance.</para>
      </summary>
      <param name="path">The object to unite with the region.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXRegion.Union(DevExpress.Drawing.DXRegion)">
      <summary>
        <para>Updates the region to the union of itself and the specified DXRegion object.</para>
      </summary>
      <param name="right">The region to unite with the current region.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXRegion.Union(System.Drawing.RectangleF)">
      <summary>
        <para>Updates the region to the union of itself and the specified RectangleF structure.</para>
      </summary>
      <param name="rect">An object to unite with the current region.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXRegion.Xor(DevExpress.Drawing.DXGraphicsPath)">
      <summary>
        <para>Updates the region to include the union minus the intersection of itself with the specified DXGraphicsPath object.</para>
      </summary>
      <param name="path">The DXGraphicsPath object to use in a XOR (Exclusive OR) operation with the current region.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXRegion.Xor(DevExpress.Drawing.DXRegion)">
      <summary>
        <para>Updates this region to the union minus the intersection of itself with the specified region.</para>
      </summary>
      <param name="right">The region to use in a XOR (Exclusive OR) operation with the current region.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXRegion.Xor(System.Drawing.RectangleF)">
      <summary>
        <para>Updates the region to include the union minus the intersection of itself with the specified rectangle.</para>
      </summary>
      <param name="rect">The rectangle to use in a XOR (Exclusive OR) operation with the current region.</param>
    </member>
    <member name="T:DevExpress.Drawing.DXRotateFlipType">
      <summary>
        <para>Lists values that indicate the rotation and flip type.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXRotateFlipType.Rotate180FlipNone">
      <summary>
        <para>A 180-degree clockwise rotation without flipping.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXRotateFlipType.Rotate180FlipX">
      <summary>
        <para>A 180-degree clockwise rotation followed by a horizontal flip.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXRotateFlipType.Rotate180FlipXY">
      <summary>
        <para>A 180-degree clockwise rotation followed by a horizontal and vertical flip.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXRotateFlipType.Rotate180FlipY">
      <summary>
        <para>A 180-degree clockwise rotation followed by a vertical flip.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXRotateFlipType.Rotate270FlipNone">
      <summary>
        <para>A 270-degree clockwise rotation without flipping.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXRotateFlipType.Rotate270FlipX">
      <summary>
        <para>A 270-degree clockwise rotation followed by a horizontal flip.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXRotateFlipType.Rotate270FlipXY">
      <summary>
        <para>A 270-degree clockwise rotation followed by a horizontal and vertical flip.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXRotateFlipType.Rotate270FlipY">
      <summary>
        <para>A 270-degree clockwise rotation followed by a vertical flip.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXRotateFlipType.Rotate90FlipNone">
      <summary>
        <para>A 90-degree clockwise rotation without flipping.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXRotateFlipType.Rotate90FlipX">
      <summary>
        <para>A 90-degree clockwise rotation followed by a horizontal flip.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXRotateFlipType.Rotate90FlipXY">
      <summary>
        <para>A 90-degree clockwise rotation followed by a horizontal and vertical flip.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXRotateFlipType.Rotate90FlipY">
      <summary>
        <para>A 90-degree clockwise rotation followed by a vertical flip.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXRotateFlipType.RotateNoneFlipNone">
      <summary>
        <para>No clockwise rotation and no flipping.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXRotateFlipType.RotateNoneFlipX">
      <summary>
        <para>No clockwise rotation followed by a horizontal flip.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXRotateFlipType.RotateNoneFlipXY">
      <summary>
        <para>No clockwise rotation followed by a horizontal and vertical flip.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXRotateFlipType.RotateNoneFlipY">
      <summary>
        <para>No clockwise rotation followed by a vertical flip.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Drawing.DXSmoothingMode">
      <summary>
        <para>Lists values that indicate smoothing (antialiasing) mode applied to lines and curves and the edges of filled areas.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXSmoothingMode.AntiAlias">
      <summary>
        <para>Specifies antialiased rendering.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXSmoothingMode.Default">
      <summary>
        <para>Specifies no antialiasing.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXSmoothingMode.HighQuality">
      <summary>
        <para>Specifies antialiased rendering.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXSmoothingMode.HighSpeed">
      <summary>
        <para>Specifies no antialising.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXSmoothingMode.Invalid">
      <summary>
        <para>Specifies an invalid mode.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXSmoothingMode.None">
      <summary>
        <para>Specifies no antialising.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Drawing.DXSolidBrush">
      <summary>
        <para>A single-color brush.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.DXSolidBrush.#ctor(System.Drawing.Color)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Drawing.DXSolidBrush"/> class with specified settings.</para>
      </summary>
      <param name="color">The brush color.</param>
    </member>
    <member name="M:DevExpress.Drawing.DXSolidBrush.Clone">
      <summary>
        <para>Clones the current DXSolidBrush object.</para>
      </summary>
      <returns>A copy of the DXSolidBrush object.</returns>
    </member>
    <member name="P:DevExpress.Drawing.DXSolidBrush.Color">
      <summary>
        <para>Gets or sets the color of the brush.</para>
      </summary>
      <value>The brush color.</value>
    </member>
    <member name="M:DevExpress.Drawing.DXSolidBrush.Equals(System.Object)">
      <summary>
        <para>Determines whether the specified object is equal to the current DXFont object.</para>
      </summary>
      <param name="obj">A DXFont to compare with current object.</param>
      <returns>true if the specified object is equal to the current object; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXSolidBrush.GetHashCode">
      <summary>
        <para>Returns the hash code for the current DXFont object.</para>
      </summary>
      <returns>The hash code for the current object.</returns>
    </member>
    <member name="T:DevExpress.Drawing.DXStringAlignment">
      <summary>
        <para>Lists values that indicate the type of alignment of a text string relative to its layout rectangle.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXStringAlignment.Center">
      <summary>
        <para>Text is aligned in the center of the layout rectangle.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXStringAlignment.Far">
      <summary>
        <para>The text is aligned far from the original position of the layout rectangle.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXStringAlignment.Near">
      <summary>
        <para>The text is aligned near the layout.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Drawing.DXStringFormat">
      <summary>
        <para>Contains text layout information, display manipulations, and OpenType features.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.DXStringFormat.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Drawing.DXStringFormat"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.DXStringFormat.#ctor(DevExpress.Drawing.DXStringFormat)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Drawing.DXStringFormat"/> class from the existing <see cref="T:DevExpress.Drawing.DXStringFormat"/> instance.</para>
      </summary>
      <param name="format">An object from which the new DXStringFormat object should be initialized.</param>
    </member>
    <member name="P:DevExpress.Drawing.DXStringFormat.Alignment">
      <summary>
        <para>Gets or sets the horizontal string alignment.</para>
      </summary>
      <value>An enumeration value that specifies the horizontal alignment of the string.</value>
    </member>
    <member name="M:DevExpress.Drawing.DXStringFormat.Clone">
      <summary>
        <para>Clones the DXStringFormat object.</para>
      </summary>
      <returns>A copy of the DXStringFormat object.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXStringFormat.CreateGenericDefault">
      <summary>
        <para>Creates a generic default DXStringFormat object.</para>
      </summary>
      <returns>An object with generic default settings.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXStringFormat.CreateGenericTypographic">
      <summary>
        <para>Creates a generic typographic DXStringFormat object.</para>
      </summary>
      <returns>An object with generic typographic settings.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXStringFormat.Equals(DevExpress.Drawing.DXStringFormat)">
      <summary>
        <para>Determines whether the specified object is equal to the current DXStringFormat object.</para>
      </summary>
      <param name="other">A DXStringFormat object to compare with current object.</param>
      <returns>true if the specified object is equal to the current object; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXStringFormat.Equals(System.Object)">
      <summary>
        <para>Determines whether the specified object is equal to the current DXStringFormat object.</para>
      </summary>
      <param name="obj">An object to compare with current object.</param>
      <returns>true if the specified object is equal to the current object; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.Drawing.DXStringFormat.FormatFlags">
      <summary>
        <para>Gets or sets formatting information flags.</para>
      </summary>
      <value>An enumeration value that indicates formatting information.</value>
    </member>
    <member name="M:DevExpress.Drawing.DXStringFormat.GetHashCode">
      <summary>
        <para>Returns the hash code for the current DXStringFormat object.</para>
      </summary>
      <returns>The hash code.</returns>
    </member>
    <member name="P:DevExpress.Drawing.DXStringFormat.HotkeyPrefix">
      <summary>
        <para>Gets or sets the type of display for hotkey prefixes that relate to text.</para>
      </summary>
      <value>An enumeration value that indicates the hotkey prefix display type.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXStringFormat.LineAlignment">
      <summary>
        <para>Gets or sets the string’s vertical alignment.’</para>
      </summary>
      <value>An enumeration value that indicates the vertical line alignment.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXStringFormat.TabStops">
      <summary>
        <para>Gets or sets tab stops for the DXStringFormat object.</para>
      </summary>
      <value>The number of spaces between the beginning of a text line and the first tab stop.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXStringFormat.Trimming">
      <summary>
        <para>Gets or sets how trim text when it exceeds the edges of the layout rectangle.</para>
      </summary>
      <value>An enumeration value that indicates how to trim characters from a string that does not fit into a layout shape.</value>
    </member>
    <member name="T:DevExpress.Drawing.DXStringFormatFlags">
      <summary>
        <para>Specifies the display and layout information for text strings.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXStringFormatFlags.DirectionRightToLeft">
      <summary>
        <para>Text has the right-to-left text direction.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXStringFormatFlags.DirectionVertical">
      <summary>
        <para>Text is vertically aligned.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXStringFormatFlags.DisplayFormatControl">
      <summary>
        <para>Control characters such as the left-to-right mark are shown in the output with a glyph.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXStringFormatFlags.FitBlackBox">
      <summary>
        <para>Parts of characters are allowed to overhang the string’s layout rectangle.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXStringFormatFlags.LineLimit">
      <summary>
        <para>Only entire lines are laid out in the formatting rectangle.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXStringFormatFlags.MeasureTrailingSpaces">
      <summary>
        <para>Includes the trailing space at the end of each line.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXStringFormatFlags.NoClip">
      <summary>
        <para>Overhanging glyph parts and unwrapped text reaching outside the formatting rectangle are allowed to be displayed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXStringFormatFlags.NoFontFallback">
      <summary>
        <para>Fallback to alternate fonts for characters not supported in the requested font is disabled. Any missing characters are displayed with the font missing glyph, usually an open square.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXStringFormatFlags.NoWrap">
      <summary>
        <para>Text wrapping between lines is disabled. This flag is implied when a point is passed instead of a rectangle, or when the specified text rectangle has a zero line length.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Drawing.DXStringTrimming">
      <summary>
        <para>Lists values that indicate how to trim characters from a string that does not completely fit into a layout shape.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXStringTrimming.Character">
      <summary>
        <para>The text is trimmed to the nearest character.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXStringTrimming.EllipsisCharacter">
      <summary>
        <para>The text is trimmed to the nearest character, and an ellipsis is inserted at the end of a trimmed line.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXStringTrimming.EllipsisPath">
      <summary>
        <para>The center is removed from trimmed lines and replaced by an ellipsis. The algorithm keeps as much of the last slash-delimited segment of the line as possible.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXStringTrimming.EllipsisWord">
      <summary>
        <para>The text is trimmed to the nearest word, and an ellipsis is inserted at the end of a trimmed line.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXStringTrimming.None">
      <summary>
        <para>No trimming is specified.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXStringTrimming.Word">
      <summary>
        <para>The text is trimmed to the nearest word.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Drawing.DXSvgImage">
      <summary>
        <para>Defines a vector image.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.DXSvgImage.Clone">
      <summary>
        <para>Clones the current DXSvgImage object.</para>
      </summary>
      <returns>A copy of the DXSvgImage object.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXSvgImage.Equals(DevExpress.Drawing.DXSvgImage)">
      <summary>
        <para>Determines whether the specified object is equal to the current DXSvgImage instance.</para>
      </summary>
      <param name="other">An object to compare.</param>
      <returns>true if the object parameter has the same property values as the current DXSvgImage instance; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXSvgImage.RotateFlip(DevExpress.Drawing.DXRotateFlipType)">
      <summary>
        <para>Rotates and flips the image.</para>
      </summary>
      <param name="rotateFlipType">An enumeration value that indicates the rotation and flip type.</param>
    </member>
    <member name="T:DevExpress.Drawing.DXTextRenderingHint">
      <summary>
        <para>Lists values that indicate the quality of text rendering.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXTextRenderingHint.AntiAlias">
      <summary>
        <para>An antialiased glyph bitmap without hinting is used to draw each character. Better quality due to antialiasing. Stem width differences may be noticeable because hinting is turned off.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXTextRenderingHint.AntiAliasGridFit">
      <summary>
        <para>An antialiased glyph bitmap with hinting is used to draw each character. Much better quality due to antialiasing, but at a higher performance cost.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXTextRenderingHint.ClearTypeGridFit">
      <summary>
        <para>A glyph ClearType bitmap with hinting is used to draw each character. The highest quality setting. Used to take advantage of ClearType font features.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXTextRenderingHint.SingleBitPerPixel">
      <summary>
        <para>A glyph bitmap is used to draw each character. Hinting is not used.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXTextRenderingHint.SingleBitPerPixelGridFit">
      <summary>
        <para>A glyph bitmap is used to draw each character. Hinting is used to improve character appearance on stems and curvatures.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXTextRenderingHint.SystemDefault">
      <summary>
        <para>A glyph bitmap with system default rendering hint is used to draw each character. The text is drawn using font-smoothing settings the user has selected for the system.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Drawing.DXTextureBrush">
      <summary>
        <para>A brush that uses an image to fill a shape interior.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.DXTextureBrush.#ctor(DevExpress.Drawing.DXImage,DevExpress.Drawing.DXWrapMode)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Drawing.DXTextureBrush"/> class with specified settings.</para>
      </summary>
      <param name="image">An image used in the brush.</param>
      <param name="wrapMode">An enumeration value that indicates how shapes are filled</param>
    </member>
    <member name="M:DevExpress.Drawing.DXTextureBrush.Clone">
      <summary>
        <para>Clones the current DXTextureBrush instance.</para>
      </summary>
      <returns>A copy of the current DXTextureBrush instance.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXTextureBrush.Dispose">
      <summary>
        <para>Disposes of the DXTextureBrush object.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.DXTextureBrush.Equals(System.Object)">
      <summary>
        <para>Determines whether the specified object is equal to the current DXTextureBrush instance.</para>
      </summary>
      <param name="obj">An object to test.</param>
      <returns>true if the obj parameter has the same property values as the current DXTextureBrush instance; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXTextureBrush.GetHashCode">
      <summary>
        <para>Returns the hash code for the current DXHatchBrush object.</para>
      </summary>
      <returns>The hash code for the current object.</returns>
    </member>
    <member name="P:DevExpress.Drawing.DXTextureBrush.Image">
      <summary>
        <para>Obtains an image used in the brush.</para>
      </summary>
      <value>An image with which the brush fills shapes.</value>
    </member>
    <member name="T:DevExpress.Drawing.DXTilingBrush">
      <summary>
        <para>Base class for pattern brushes.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.DXTilingBrush.Equals(System.Object)">
      <summary>
        <para>Determines whether the specified object is equal to the current DXTilingBrush instance.</para>
      </summary>
      <param name="obj">An object to test.</param>
      <returns>true if the object parameter has the same property values as the current DXTilingBrush instance; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Drawing.DXTilingBrush.GetHashCode">
      <summary>
        <para>Gets the hash code for DXTilingBrush.</para>
      </summary>
      <returns>The hash code.</returns>
    </member>
    <member name="P:DevExpress.Drawing.DXTilingBrush.Transform">
      <summary>
        <para>Gets or sets a copy of the geometric world transformation.</para>
      </summary>
      <value>A copy of the DXMatrix that is the geometric world transformation.</value>
    </member>
    <member name="P:DevExpress.Drawing.DXTilingBrush.WrapMode">
      <summary>
        <para>Gets or sets the brush’s wrap mode.</para>
      </summary>
      <value>An enumeration value that indicates the wrap mode.</value>
    </member>
    <member name="T:DevExpress.Drawing.DXWrapMode">
      <summary>
        <para>Lists values that indicate how to lite a texture or gradient it is smaller than the area being filled.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXWrapMode.Clamp">
      <summary>
        <para>The texture or gradient is not tiled.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXWrapMode.Tile">
      <summary>
        <para>Tiles the gradient or texture.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXWrapMode.TileFlipX">
      <summary>
        <para>Reverses the texture or gradient horizontally and then tiles the texture or gradient.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXWrapMode.TileFlipXY">
      <summary>
        <para>Reverses the texture or gradient horizontally and vertically and then tiles the texture or gradient.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.DXWrapMode.TileFlipY">
      <summary>
        <para>Reverses the texture or gradient vertically and then tiles the texture or gradient.</para>
      </summary>
    </member>
    <member name="N:DevExpress.Drawing.Extensions">
      <summary>
        <para>Contains extension members for the DevExpress.Drawing library.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Drawing.Extensions.DXGraphicsUnitExtensions">
      <summary>
        <para>Contains extension methods for the <see cref="T:DevExpress.Drawing.DXGraphicsUnit"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.Extensions.DXGraphicsUnitExtensions.ToDpi(DevExpress.Drawing.DXGraphicsUnit)">
      <summary>
        <para>Converts a DXGraphicsUnit object to a DPI number.</para>
      </summary>
      <param name="unit">An enumeration value that indicates the unit type.</param>
      <returns>The DPI number.</returns>
    </member>
    <member name="M:DevExpress.Drawing.Extensions.DXGraphicsUnitExtensions.ToDpiI(DevExpress.Drawing.DXGraphicsUnit)">
      <summary>
        <para>Converts a DXGraphicsUnit object to a DPI number using the device-independent Pixel constant.</para>
      </summary>
      <param name="unit">An enumeration value to convert.</param>
      <returns>The DPI number.</returns>
    </member>
    <member name="T:DevExpress.Drawing.Extensions.DXImageExtensions">
      <summary>
        <para>Contains extension methods for the <see cref="T:DevExpress.Drawing.DXImage"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.Extensions.DXImageExtensions.ConvertToGdiPlusImage(DevExpress.Drawing.DXImage)">
      <summary>
        <para>Converts a DXImage object to the Image object.</para>
      </summary>
      <param name="image">The object to convert.</param>
      <returns>The resulting image.</returns>
    </member>
    <member name="T:DevExpress.Drawing.NotFoundFontEventArgs">
      <summary>
        <para>Contains data for the <see cref="E:DevExpress.Drawing.DXFontRepository.QueryNotFoundFont"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.NotFoundFontEventArgs.#ctor(System.String,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Drawing.NotFoundFontEventArgs"/> class with specified settings.</para>
      </summary>
      <param name="requestedFont">The name of the font referenced in the document.</param>
      <param name="actualFont">The name of the font referenced in the document.</param>
    </member>
    <member name="P:DevExpress.Drawing.NotFoundFontEventArgs.ActualFont">
      <summary>
        <para>Gets the name of the font that is available in the current hosting environment as a substitute for the missing font.</para>
      </summary>
      <value>A <see cref="T:System.String"/> that specifies the font name.</value>
    </member>
    <member name="P:DevExpress.Drawing.NotFoundFontEventArgs.FontFileData">
      <summary>
        <para>Gets or sets the font file data to be used in place of the missing font.</para>
      </summary>
      <value>The font data in the <see cref="T:System.Byte"/> format.</value>
    </member>
    <member name="P:DevExpress.Drawing.NotFoundFontEventArgs.RequestedFont">
      <summary>
        <para>Gets the name of the font referenced in the document, but missing in the current hosting environment.</para>
      </summary>
      <value>A <see cref="T:System.String"/> that specifies the font name.</value>
    </member>
    <member name="N:DevExpress.Drawing.Printing">
      <summary>
        <para>Contains cross-platform counterparts of the System.Drawing.Printing classes not supported in non-Windows environments.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Drawing.Printing.DXDuplexMode">
      <summary>
        <para>Lists values that specify the duplex printing mode.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXDuplexMode.Default">
      <summary>
        <para>Default mode (Simplex).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXDuplexMode.DuplexLongEdge">
      <summary>
        <para>Pages are bound along the long edge of the page.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXDuplexMode.DuplexShortEdge">
      <summary>
        <para>Pages are bound along the short edge of the page.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXDuplexMode.Simplex">
      <summary>
        <para>Printing on one side of the paper.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Drawing.Printing.DXPaperKind">
      <summary>
        <para>Specifies standard paper sizes.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.A2">
      <summary>
        <para>A2 paper (420 mm by 594 mm).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.A3">
      <summary>
        <para>A3 paper (297 mm by 420 mm).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.A3Extra">
      <summary>
        <para>A3 extra paper (322 mm by 445 mm).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.A3ExtraTransverse">
      <summary>
        <para>A3 extra transverse paper (322 mm by 445 mm).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.A3Rotated">
      <summary>
        <para>A3 rotated paper (420 mm by 297 mm).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.A3Transverse">
      <summary>
        <para>A3 transverse paper (297 mm by 420 mm).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.A4">
      <summary>
        <para>A4 paper (210 mm by 297 mm).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.A4Extra">
      <summary>
        <para>A4 extra paper (236 mm by 322 mm). This value is specific to the PostScript driver and is used only by Linotronic printers to help save paper.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.A4Plus">
      <summary>
        <para>A4 plus paper (210 mm by 330 mm).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.A4Rotated">
      <summary>
        <para>A4 rotated paper (297 mm by 210 mm). Requires Windows NT 4.0 or later.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.A4Small">
      <summary>
        <para>A4 small paper (210 mm by 297 mm).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.A4Transverse">
      <summary>
        <para>A4 transverse paper (210 mm by 297 mm).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.A5">
      <summary>
        <para>A5 paper (148 mm by 210 mm).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.A5Extra">
      <summary>
        <para>A5 extra paper (174 mm by 235 mm).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.A5Rotated">
      <summary>
        <para>A5 rotated paper (210 mm by 148 mm).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.A5Transverse">
      <summary>
        <para>A5 transverse paper (148 mm by 210 mm).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.A6">
      <summary>
        <para>A6 paper (105 mm by 148 mm). Requires Windows NT 4.0 or later.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.A6Rotated">
      <summary>
        <para>A6 rotated paper (148 mm by 105 mm). Requires Windows NT 4.0 or later.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.APlus">
      <summary>
        <para>SuperA/SuperA/A4 paper (227 mm by 356 mm).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.B4">
      <summary>
        <para>B4 paper (250 mm by 353 mm).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.B4Envelope">
      <summary>
        <para>B4 envelope (250 mm by 353 mm).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.B4JisRotated">
      <summary>
        <para>JIS B4 rotated paper (364 mm by 257 mm). Requires Windows NT 4.0 or later.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.B5">
      <summary>
        <para>B5 paper (176 mm by 250 mm).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.B5Envelope">
      <summary>
        <para>B5 envelope (176 mm by 250 mm).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.B5Extra">
      <summary>
        <para>ISO B5 extra paper (201 mm by 276 mm).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.B5JisRotated">
      <summary>
        <para>JIS B5 rotated paper (257 mm by 182 mm). Requires Windows NT 4.0 or later.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.B5Transverse">
      <summary>
        <para>JIS B5 transverse paper (182 mm by 257 mm).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.B6Envelope">
      <summary>
        <para>B6 envelope (176 mm by 125 mm).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.B6Jis">
      <summary>
        <para>JIS B6 paper (128 mm by 182 mm). Requires Windows NT 4.0 or later.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.B6JisRotated">
      <summary>
        <para>JIS B6 rotated paper (182 mm by 128 mm). Requires Windows NT 4.0 or later.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.BPlus">
      <summary>
        <para>SuperB/SuperB/A3 paper (305 mm by 487 mm).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.C3Envelope">
      <summary>
        <para>C3 envelope (324 mm by 458 mm).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.C4Envelope">
      <summary>
        <para>C4 envelope (229 mm by 324 mm).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.C5Envelope">
      <summary>
        <para>C5 envelope (162 mm by 229 mm).s</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.C65Envelope">
      <summary>
        <para>C6 envelope (114 mm by 162 mm).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.C6Envelope">
      <summary>
        <para>C65 envelope (114 mm by 229 mm).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.CSheet">
      <summary>
        <para>C paper (17 in. by 22 in.).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.Custom">
      <summary>
        <para>The paper size is defined by the user.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.DLEnvelope">
      <summary>
        <para>DL envelope (110 mm by 220 mm).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.DSheet">
      <summary>
        <para>D paper (22 in. by 34 in.).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.ESheet">
      <summary>
        <para>E paper (34 in. by 44 in.).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.Executive">
      <summary>
        <para>Executive paper (7.25 in. by 10.5 in.).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.Folio">
      <summary>
        <para>Folio paper (8.5 in. by 13 in.).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.GermanLegalFanfold">
      <summary>
        <para>German legal fanfold (8.5 in. by 13 in.).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.GermanStandardFanfold">
      <summary>
        <para>German standard fanfold (8.5 in. by 12 in.).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.InviteEnvelope">
      <summary>
        <para>Invitation envelope (220 mm by 220 mm).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.IsoB4">
      <summary>
        <para>ISO B4 (250 mm by 353 mm).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.ItalyEnvelope">
      <summary>
        <para>Italy envelope (110 mm by 230 mm).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.JapaneseDoublePostcard">
      <summary>
        <para>Japanese double postcard (200 mm by 148 mm). Requires Windows NT 4.0 or later.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.JapaneseDoublePostcardRotated">
      <summary>
        <para>Japanese rotated double postcard (148 mm by 200 mm). Requires Windows NT 4.0 or later.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.JapaneseEnvelopeChouNumber3">
      <summary>
        <para>Japanese Chou #3 envelope. Requires Windows NT 4.0 or later.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.JapaneseEnvelopeChouNumber3Rotated">
      <summary>
        <para>Japanese rotated Chou #3 envelope. Requires Windows NT 4.0 or later.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.JapaneseEnvelopeChouNumber4">
      <summary>
        <para>Japanese Chou #4 envelope. Requires Windows NT 4.0 or later.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.JapaneseEnvelopeChouNumber4Rotated">
      <summary>
        <para>Japanese rotated Chou #4 envelope. Requires Windows NT 4.0 or later.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.JapaneseEnvelopeKakuNumber2">
      <summary>
        <para>Japanese Kaku #2 envelope. Requires Windows NT 4.0 or later.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.JapaneseEnvelopeKakuNumber2Rotated">
      <summary>
        <para>Japanese rotated Kaku #2 envelope. Requires Windows NT 4.0 or later.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.JapaneseEnvelopeKakuNumber3">
      <summary>
        <para>Japanese Kaku #3 envelope. Requires Windows NT 4.0 or later.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.JapaneseEnvelopeKakuNumber3Rotated">
      <summary>
        <para>Japanese rotated Kaku #3 envelope. Requires Windows NT 4.0 or later.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.JapaneseEnvelopeYouNumber4">
      <summary>
        <para>Japanese You #4 envelope. Requires Windows NT 4.0 or later.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.JapaneseEnvelopeYouNumber4Rotated">
      <summary>
        <para>Japanese You #4 rotated envelope. Requires Windows NT 4.0 or later.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.JapanesePostcard">
      <summary>
        <para>Japanese postcard (100 mm by 148 mm).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.JapanesePostcardRotated">
      <summary>
        <para>Japanese rotated postcard (148 mm by 100 mm). Requires Windows NT 4.0 or later.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.Ledger">
      <summary>
        <para>Ledger paper (17 in. by 11 in.).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.Legal">
      <summary>
        <para>Legal paper (8.5 in. by 14 in.).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.LegalExtra">
      <summary>
        <para>Legal extra paper (9.275 in. by 15 in.). This value is specific to the PostScript driver and is used only by Linotronic printers in order to conserve paper.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.Letter">
      <summary>
        <para>Letter paper (8.5 in. by 11 in.).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.LetterExtra">
      <summary>
        <para>Letter extra paper (9.275 in. by 12 in.). This value is specific to the PostScript driver and is used only by Linotronic printers in order to conserve paper.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.LetterExtraTransverse">
      <summary>
        <para>Letter extra transverse paper (9.275 in. by 12 in.).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.LetterPlus">
      <summary>
        <para>Letter plus paper (8.5 in. by 12.69 in.).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.LetterRotated">
      <summary>
        <para>Letter rotated paper (11 in. by 8.5 in.).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.LetterSmall">
      <summary>
        <para>Letter small paper (8.5 in. by 11 in.).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.LetterTransverse">
      <summary>
        <para>Letter transverse paper (8.275 in. by 11 in.).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.MonarchEnvelope">
      <summary>
        <para>Monarch envelope (3.875 in. by 7.5 in.).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.Note">
      <summary>
        <para>Note paper (8.5 in. by 11 in.).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.Number10Envelope">
      <summary>
        <para>#10 envelope (4.125 in. by 9.5 in.).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.Number11Envelope">
      <summary>
        <para>#11 envelope (4.5 in. by 10.375 in.).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.Number12Envelope">
      <summary>
        <para>#12 envelope (4.75 in. by 11 in.).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.Number14Envelope">
      <summary>
        <para>#14 envelope (5 in. by 11.5 in.).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.Number9Envelope">
      <summary>
        <para>#9 envelope (3.875 in. by 8.875 in.).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.PersonalEnvelope">
      <summary>
        <para>6 3/4 envelope (3.625 in. by 6.5 in.).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.Prc16K">
      <summary>
        <para>16K paper (146 mm by 215 mm). Requires Windows NT 4.0 or later.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.Prc16KRotated">
      <summary>
        <para>16K rotated paper (146 mm by 215 mm). Requires Windows NT 4.0 or later.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.Prc32K">
      <summary>
        <para>32K paper (97 mm by 151 mm). Requires Windows NT 4.0 or later.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.Prc32KBig">
      <summary>
        <para>32K big paper (97 mm by 151 mm). Requires Windows NT 4.0 or later.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.Prc32KBigRotated">
      <summary>
        <para>32K big rotated paper (97 mm by 151 mm). Requires Windows NT 4.0 or later.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.Prc32KRotated">
      <summary>
        <para>32K rotated paper (97 mm by 151 mm). Requires Windows NT 4.0 or later.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.PrcEnvelopeNumber1">
      <summary>
        <para>#1 rotated envelope (165 mm by 102 mm). Requires Windows NT 4.0 or later.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.PrcEnvelopeNumber10">
      <summary>
        <para>#10 envelope (324 mm by 458 mm). Requires Windows NT 4.0 or later.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.PrcEnvelopeNumber10Rotated">
      <summary>
        <para>#10 rotated envelope (458 mm by 324 mm). Requires Windows NT 4.0 or later.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.PrcEnvelopeNumber1Rotated">
      <summary>
        <para>#1 rotated envelope (165 mm by 102 mm). Requires Windows NT 4.0 or later.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.PrcEnvelopeNumber2">
      <summary>
        <para>#2 envelope (102 mm by 176 mm). Requires Windows NT 4.0 or later.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.PrcEnvelopeNumber2Rotated">
      <summary>
        <para>#2 rotated envelope (176 mm by 102 mm). Requires Windows NT 4.0 or later.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.PrcEnvelopeNumber3">
      <summary>
        <para>#3 envelope (125 mm by 176 mm). Requires Windows NT 4.0 or later.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.PrcEnvelopeNumber3Rotated">
      <summary>
        <para>#3 rotated envelope (176 mm by 125 mm). Requires Windows NT 4.0 or later.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.PrcEnvelopeNumber4">
      <summary>
        <para>#4 envelope (110 mm by 208 mm). Requires Windows NT 4.0 or later.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.PrcEnvelopeNumber4Rotated">
      <summary>
        <para>#4 rotated envelope (208 mm by 110 mm). Requires Windows NT 4.0 or later.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.PrcEnvelopeNumber5">
      <summary>
        <para>#5 envelope (110 mm by 220 mm). Requires Windows NT 4.0 or later.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.PrcEnvelopeNumber5Rotated">
      <summary>
        <para>Envelope #5 rotated envelope (220 mm by 110 mm). Requires Windows NT 4.0 or later.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.PrcEnvelopeNumber6">
      <summary>
        <para>#6 envelope (120 mm by 230 mm). Requires Windows NT 4.0 or later.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.PrcEnvelopeNumber6Rotated">
      <summary>
        <para>#6 rotated envelope (230 mm by 120 mm). Requires Windows NT 4.0 or later.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.PrcEnvelopeNumber7">
      <summary>
        <para>#7 envelope (160 mm by 230 mm). Requires Windows NT 4.0 or later.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.PrcEnvelopeNumber7Rotated">
      <summary>
        <para>#7 rotated envelope (230 mm by 160 mm). Requires Windows NT 4.0 or later.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.PrcEnvelopeNumber8">
      <summary>
        <para>#8 envelope (120 mm by 309 mm). Requires Windows NT 4.0 or later.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.PrcEnvelopeNumber8Rotated">
      <summary>
        <para>#8 rotated envelope (309 mm by 120 mm). Requires Windows NT 4.0 or later.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.PrcEnvelopeNumber9">
      <summary>
        <para>#9 envelope (229 mm by 324 mm). Requires Windows NT 4.0 or later.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.PrcEnvelopeNumber9Rotated">
      <summary>
        <para>#9 rotated envelope (324 mm by 229 mm). Requires Windows NT 4.0 or later.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.Quarto">
      <summary>
        <para>Quarto paper (215 mm by 275 mm).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.Standard10x11">
      <summary>
        <para>Standard paper (10 in. by 11 in.).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.Standard10x14">
      <summary>
        <para>Standard paper (10 in. by 14 in.).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.Standard11x17">
      <summary>
        <para>Standard paper (11 in. by 17 in.).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.Standard12x11">
      <summary>
        <para>Standard paper (12 in. by 11 in.). Requires Windows NT 4.0 or later.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.Standard15x11">
      <summary>
        <para>Standard paper (15 in. by 11 in.).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.Standard9x11">
      <summary>
        <para>Standard paper (9 in. by 11 in.).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.Statement">
      <summary>
        <para>Statement paper (5.5 in. by 8.5 in.).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.Tabloid">
      <summary>
        <para>Tabloid paper (11 in. by 17 in.).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.TabloidExtra">
      <summary>
        <para>Tabloid extra paper (11.69 in. by 18 in.). This value is specific to the PostScript driver and is used only by Linotronic printers in order to conserve paper.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Drawing.Printing.DXPaperKind.USStandardFanfold">
      <summary>
        <para>US standard fanfold (14.875 in. by 11 in.).</para>
      </summary>
    </member>
    <member name="T:DevExpress.Drawing.Printing.DXPaperSize">
      <summary>
        <para>Defines a paper size.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.Printing.DXPaperSize.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Drawing.Printing.DXPaperSize"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.Printing.DXPaperSize.#ctor(System.String,System.Int32,System.Int32)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Drawing.Printing.DXPaperSize"/> class with specified settings.</para>
      </summary>
      <param name="name">the name of the paper.</param>
      <param name="width">The width of the paper, in hundredths of an inch.</param>
      <param name="height">The height of the paper, in hundredths of an inch.</param>
    </member>
    <member name="P:DevExpress.Drawing.Printing.DXPaperSize.Height">
      <summary>
        <para>Gets or sets the paper height.</para>
      </summary>
      <value>The height of the paper, in hundredths of an inch.</value>
    </member>
    <member name="P:DevExpress.Drawing.Printing.DXPaperSize.Kind">
      <summary>
        <para>Gets the type of paper.</para>
      </summary>
      <value>An enumeration value that indicates the paper type.</value>
    </member>
    <member name="P:DevExpress.Drawing.Printing.DXPaperSize.PaperName">
      <summary>
        <para>Gets or sets the paper name.</para>
      </summary>
      <value>The name of the paper.</value>
    </member>
    <member name="P:DevExpress.Drawing.Printing.DXPaperSize.RawKind">
      <summary>
        <para>Gets or sets a type of paper by its index.</para>
      </summary>
      <value>An integer that indicates one of the <see cref="T:DevExpress.Drawing.Printing.DXPaperKind">DXPaperKind</see> values or a custom value.</value>
    </member>
    <member name="M:DevExpress.Drawing.Printing.DXPaperSize.ToString">
      <summary>
        <para>Returns a string that specifies `DXPaperSize’.</para>
      </summary>
      <returns>A string that specifies `DXPaperSize’.</returns>
    </member>
    <member name="P:DevExpress.Drawing.Printing.DXPaperSize.Width">
      <summary>
        <para>Gets or sets the paper width.</para>
      </summary>
      <value>The width of the paper, in hundredths of an inch.</value>
    </member>
    <member name="T:DevExpress.Drawing.Printing.DXPrinterSettings">
      <summary>
        <para>Contains printer options.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.Printing.DXPrinterSettings.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Drawing.Printing.DXPrinterSettings"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Drawing.Printing.DXPrinterSettings.Assign(DevExpress.Drawing.Printing.DXPrinterSettings)">
      <summary>
        <para>Copies all settings from the object passed as a parameter.</para>
      </summary>
      <param name="settings">The source DXPrinterSettings object.</param>
    </member>
    <member name="P:DevExpress.Drawing.Printing.DXPrinterSettings.Collate">
      <summary>
        <para>Specifies whether the printed document is collated.</para>
      </summary>
      <value>true to collate the printed document; otherwise, false</value>
    </member>
    <member name="P:DevExpress.Drawing.Printing.DXPrinterSettings.Copies">
      <summary>
        <para>Specifies the number of copies.</para>
      </summary>
      <value>The number of copies.</value>
    </member>
    <member name="P:DevExpress.Drawing.Printing.DXPrinterSettings.Duplex">
      <summary>
        <para>Specifies whether and how to print on both sides of the paper.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Drawing.Printing.DXDuplexMode"/> value that specifies the duplex mode.</value>
    </member>
    <member name="P:DevExpress.Drawing.Printing.DXPrinterSettings.PageRange">
      <summary>
        <para>Specifies the range of pages for printing.</para>
      </summary>
      <value>A <see cref="T:System.String"/> that defines the page range.</value>
    </member>
    <member name="P:DevExpress.Drawing.Printing.DXPrinterSettings.PrinterName">
      <summary>
        <para>Specifies the name of the printer where the document is sent.</para>
      </summary>
      <value>A <see cref="T:System.String"/> that is the printer name.</value>
    </member>
    <member name="T:DevExpress.Drawing.Settings">
      <summary>
        <para>Contains drawing engine settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Drawing.Settings.DrawingEngine">
      <summary>
        <para>Specifies the engine used to render content.</para>
      </summary>
      <value>An enumeration value that indicates the drawing engine.</value>
    </member>
    <member name="N:DevExpress.Utils.Svg">
      <summary>
        <para>Provides an API that supports <see href="https://docs.devexpress.com/WindowsForms/117631/devexpress-icon-library/how-to-draw-and-use-svg-images">vector images</see>.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Utils.Svg.SvgBitmap">
      <summary>
        <para>A raster image created from a vector icon.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Utils.Svg.SvgBitmap.#ctor(DevExpress.Utils.Svg.SvgImage)">
      <summary>
        <para>Creates a new instance of the <see cref="T:DevExpress.Utils.Svg.SvgBitmap"/> class from the specified vector image.</para>
      </summary>
      <param name="svgImage">A <see href="https://docs.devexpress.com/WindowsForms/117631/devexpress-icon-library/how-to-draw-and-use-svg-images">vector image</see>.</param>
    </member>
    <member name="M:DevExpress.Utils.Svg.SvgBitmap.ClearCache(System.Boolean)">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <param name="disposeImages">true to dispose the images; otherwise, false.</param>
    </member>
    <member name="M:DevExpress.Utils.Svg.SvgBitmap.Create(DevExpress.Utils.Svg.SvgImage)">
      <summary>
        <para>Creates a new SvgBitmap object from the specified vector image.</para>
      </summary>
      <param name="svgImage">The vector image.</param>
      <returns>The SvgBitmap object.</returns>
    </member>
    <member name="M:DevExpress.Utils.Svg.SvgBitmap.Create(System.Drawing.Size)">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <param name="size">The image size.</param>
      <returns>The SvgBitmap object.</returns>
    </member>
    <member name="F:DevExpress.Utils.Svg.SvgBitmap.DisabledTag">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value>The object.</value>
    </member>
    <member name="M:DevExpress.Utils.Svg.SvgBitmap.DisposeBufferedImage">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Utils.Svg.SvgBitmap.DrawingOffset">
      <summary>
        <para>Gets or sets the image offset.</para>
      </summary>
      <value>A System.Drawing.PointF structure that specifies the image offset.</value>
    </member>
    <member name="P:DevExpress.Utils.Svg.SvgBitmap.Elements">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value>A List of vector image elements.</value>
    </member>
    <member name="M:DevExpress.Utils.Svg.SvgBitmap.ForceHighSpeedRendering(System.Action)">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <param name="render">The action.</param>
    </member>
    <member name="M:DevExpress.Utils.Svg.SvgBitmap.ForceHighSpeedRendering``1(System.Func{``0})">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Utils.Svg.SvgBitmap.FromFile(System.String)">
      <summary>
        <para>Creates a new SvgBitmap object from the specified vector image.</para>
      </summary>
      <param name="path">A path to the image file.</param>
      <returns>The SvgBitmap object.</returns>
    </member>
    <member name="M:DevExpress.Utils.Svg.SvgBitmap.FromStream(System.IO.Stream)">
      <summary>
        <para>Creates a new SvgBitmap object from the specified stream that stores a vector image.</para>
      </summary>
      <param name="stream">The stream that stores the image.</param>
      <returns>The SvgBitmap object.</returns>
    </member>
    <member name="M:DevExpress.Utils.Svg.SvgBitmap.GetBounds(System.Boolean)">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Utils.Svg.SvgBitmap.HitTest(System.Drawing.PointF,System.Double)">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <param name="point">The PointF structure that specifies the coordinates of the test-point.</param>
    </member>
    <member name="M:DevExpress.Utils.Svg.SvgBitmap.Render(DevExpress.Utils.Design.ISvgPaletteProvider,System.Double,DevExpress.Utils.DefaultBoolean,DevExpress.Utils.DefaultBoolean)">
      <summary>
        <para>Returns a raster image with the specified scale factor based on the SVG bitmap.</para>
      </summary>
      <param name="paletteProvider">A color palette of the current application skin and visual element state.</param>
      <param name="scaleFactor">The scale factor. 1 to draw an image as is.</param>
      <param name="useHighSpeedRendering">DefaultBoolean.True to enable high-speed rendering; otherwise, DefaultBoolean.False.</param>
      <param name="allowCache">DefaultBoolean.True to enable image cache; otherwise, DefaultBoolean.False.</param>
      <returns>A raster image based on the SVG bitmap.</returns>
    </member>
    <member name="M:DevExpress.Utils.Svg.SvgBitmap.Render(System.Drawing.Size,DevExpress.Utils.Design.ISvgPaletteProvider,DevExpress.Utils.DefaultBoolean,DevExpress.Utils.DefaultBoolean)">
      <summary>
        <para>Returns a raster image with the specified size based on the SVG bitmap.</para>
      </summary>
      <param name="imageSize">The image size, in pixels.</param>
      <param name="paletteProvider">A color palette of the current application skin and visual element state.</param>
      <param name="useHighSpeedRendering">DefaultBoolean.True to enable high-speed rendering; otherwise, DefaultBoolean.False.</param>
      <param name="allowCache">DefaultBoolean.True to enable image cache; otherwise, DefaultBoolean.False.</param>
      <returns>A raster image based on the SVG bitmap.</returns>
    </member>
    <member name="M:DevExpress.Utils.Svg.SvgBitmap.RenderToDXGraphics(DevExpress.Drawing.Internal.IDXGraphics,DevExpress.Utils.Design.ISvgPaletteProvider,System.Double,DevExpress.Utils.DefaultBoolean)">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Utils.Svg.SvgBitmap.RenderToGraphics(System.Drawing.Graphics,DevExpress.Utils.Design.ISvgPaletteProvider,System.Double,DevExpress.Utils.DefaultBoolean)">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Utils.Svg.SvgBitmap.RenderToSvgGraphics(DevExpress.Utils.Svg.ISvgGraphics,DevExpress.Utils.Design.ISvgPaletteProvider,System.Double)">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Utils.Svg.SvgBitmap.ResetRenderedImage(System.Boolean)">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <param name="disposeImage">true to dispose the image; otherwise, false.</param>
    </member>
    <member name="M:DevExpress.Utils.Svg.SvgBitmap.Save(System.String)">
      <summary>
        <para>Saves the image to the specified file.</para>
      </summary>
      <param name="path">A path to the file to store the image.</param>
    </member>
    <member name="P:DevExpress.Utils.Svg.SvgBitmap.Scale">
      <summary>
        <para>Gets or sets the scale factor.</para>
      </summary>
      <value>The scale factor. 1 to draw an image as is.</value>
    </member>
    <member name="P:DevExpress.Utils.Svg.SvgBitmap.SvgImage">
      <summary>
        <para>A <see href="https://docs.devexpress.com/WindowsForms/117631/devexpress-icon-library/how-to-draw-and-use-svg-images">vector image</see>.</para>
      </summary>
      <value>The SvgImage object.</value>
    </member>
    <member name="P:DevExpress.Utils.Svg.SvgBitmap.SvgImageRenderingMode">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value>The SVG image rendering mode.</value>
    </member>
    <member name="M:DevExpress.Utils.Svg.SvgBitmap.WrapElement(DevExpress.Utils.Svg.SvgElement)">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <param name="element">An element (primitive) from which the vector image consists.</param>
      <returns>An element wrapper.</returns>
    </member>
  </members>
</doc>