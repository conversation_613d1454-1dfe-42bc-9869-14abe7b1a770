Imports System.IO
Imports FireSharp.Config
Imports FireSharp.Interfaces
Imports FireSharp.Response
Imports System.Management
Imports FireSharp
Imports System.Net.Http
Imports System.Text
Imports System.Threading.Tasks

Public Class Form1
    ' إعدادات بوت تليجرام - ضع البيانات الخاصة بك هنا
    Private Const TELEGRAM_BOT_TOKEN As String = "**********************************************"
    Private Const TELEGRAM_CHAT_ID As String = "2140915988"

    Private Sub Button1_Click(sender As Object, e As EventArgs) Handles Button1.Click
        ClientApp.ShowDialog()
    End Sub

    Function GenerateHWID() As String
        Return GetHardDiskSerial() & "-" & GetCPUID()
    End Function

    Function GetHardDiskSerial() As String
        Try
            Dim searcher As New ManagementObjectSearcher("SELECT * FROM Win32_PhysicalMedia")
            For Each wmi_HD As ManagementObject In searcher.Get()
                If wmi_HD("SerialNumber") IsNot Nothing Then
                    Return wmi_HD("SerialNumber").ToString().Trim()
                End If
            Next
        Catch
        End Try
        Return "Unknown"
    End Function

    Function GetCPUID() As String
        Try
            Dim cpuID As String = ""
            Dim mc As New ManagementClass("win32_processor")
            Dim moc As ManagementObjectCollection = mc.GetInstances()
            For Each mo As ManagementObject In moc
                cpuID = mo.Properties("processorID").Value.ToString()
                Exit For
            Next
            Return cpuID
        Catch
        End Try
        Return "Unknown"
    End Function

    ' دالة إرسال رسالة إلى بوت تليجرام
    Private Async Function SendTelegramMessage(message As String) As Task(Of Boolean)
        Try
            Using httpClient As New HttpClient()
                Dim url As String = $"https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/sendMessage"
                Dim jsonContent As String = $"{{""chat_id"":""{TELEGRAM_CHAT_ID}"",""text"":""{message}""}}"
                Dim content As New StringContent(jsonContent, Encoding.UTF8, "application/json")

                Dim response As HttpResponseMessage = Await httpClient.PostAsync(url, content)
                Return response.IsSuccessStatusCode
            End Using
        Catch ex As Exception
            ' يمكنك إضافة معالجة للأخطاء هنا إذا أردت
            Return False
        End Try
    End Function

    Dim client As IFirebaseClient

    Private Async Sub Form1_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Dim hwid As String = GenerateHWID()
        txtHWID.Text = hwid

        ' إرسال HWID إلى بوت تليجرام
        Dim message As String = $"تم فتح المشروع - HWID: {hwid}"
        Dim success As Boolean = Await SendTelegramMessage(message)

        ' يمكنك إضافة رسالة تأكيد إذا أردت
        ' If success Then
        '     MessageBox.Show("تم إرسال HWID إلى تليجرام بنجاح")
        ' Else
        '     MessageBox.Show("فشل في إرسال HWID إلى تليجرام")
        ' End If
    End Sub
End Class
